import { getLogs, clearLogs } from './modules/log-storage.js';
import { getSetting, setSetting } from './modules/settings.js';

const refreshBtn = document.getElementById('refresh-btn');
const clearBtn = document.getElementById('clear-btn');
const logContainer = document.getElementById('log-container');
const debugLoggingCheckbox = document.getElementById('debug-logging');

async function renderLogs() {
    logContainer.innerHTML = '';
    const response = await getLogs();
    if (response.status === 'success') {
        const logs = response.data;
        logs.forEach(log => {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${log.level}`;
        entry.textContent = `[${new Date(log.timestamp).toISOString()}] [${log.level.toUpperCase()}] ${log.message}`;
            logContainer.appendChild(entry);
        });
    } else {
        console.error("Error rendering logs:", response.error);
    }
}

async function loadSettings() {
    const debugLoggingEnabled = await getSetting('debugLoggingEnabled');
    debugLoggingCheckbox.checked = debugLoggingEnabled;
}

function saveSettings() {
    const debugLoggingEnabled = debugLoggingCheckbox.checked;
    setSetting('debugLoggingEnabled', debugLoggingEnabled);
}

refreshBtn.addEventListener('click', renderLogs);
clearBtn.addEventListener('click', async () => {
    await clearLogs();
    await renderLogs();
});
debugLoggingCheckbox.addEventListener('change', saveSettings);

// Initial load
renderLogs();
loadSettings();
