<!DOCTYPE html>
<html>
<head>
  <title>LLMLog Options</title>
  <meta charset="UTF-8">
  <link rel="stylesheet" href="options.css">
</head>
<body>
  <h1>LLMLog Settings</h1>
  <div id="settings-container">
    <h2>Settings</h2>
    <label class="switch">
      <input type="checkbox" id="recording-enabled" checked>
      <span class="slider round"></span>
    </label>
    <label for="recording-enabled">Enable Conversation Recording</label>
    <br>
    <label class="switch">
      <input type="checkbox" id="debug-logging-enabled">
      <span class="slider round"></span>
    </label>
    <label for="debug-logging-enabled">Enable Debug Logging</label>
    <hr>
    
    <h2>Statistics</h2>
    <div id="statistics-container">
      <p>Loading statistics...</p>
    </div>
    <hr>

    <h2>Export Data</h2>
    <p>Export all your conversations to a local file.</p>
    <button id="export-json">Export as JSON</button>
    <button id="export-markdown">Export as Markdown</button>
  </div>
  <script src="options.js"></script>
</body>
</html>