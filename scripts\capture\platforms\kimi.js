/**
 * Platform Logic for Kimi
 */

export const config = {
    name: '<PERSON><PERSON>',
    apiEndpoint: /^\/api\/chat\/.+\/completion\//,
};

let currentConversationId = null;

export async function parseRequest(request, logger) {
    try {
        const requestBody = await request.clone().json();
        if (requestBody.messages && requestBody.messages.length > 0) {
            const userMessage = requestBody.messages.find(msg => msg.role === 'user');
            if (userMessage) {
                // Extract conversation ID from the request URL
                const url = new URL(request.url);
                const pathParts = url.pathname.split('/');
                // URL format is /api/chat/{id}/completion/...
                const chatIndex = pathParts.indexOf('chat');
                if (chatIndex > -1 && chatIndex + 1 < pathParts.length) {
                    currentConversationId = pathParts[chatIndex + 1];
                }
                return userMessage.content;
            }
        }
    } catch (e) {
        logger.error("Error parsing <PERSON><PERSON> request:", e);
    }
    return '';
}

export async function parseResponse(response, logger) {
    const sseStream = await response.clone().text();
    logger.log("<PERSON>i Raw SSE Stream:", sseStream);
    const messages = sseStream.split('\n\n').filter(Boolean);
    let fullText = '';

    for (const messageBlock of messages) {
        const lines = messageBlock.split('\n');
        let dataString = null;
        for (const line of lines) {
            if (line.startsWith('data:')) {
                dataString = line.substring(5).trim();
                break;
            }
        }

        if (!dataString) continue;

        try {
            const data = JSON.parse(dataString);
            logger.log("Kimi Parsed SSE data:", data);

            if (data.event === 'cmpl' && data.text) {
                fullText += data.text;
            }
        } catch (e) {
            // Ignore ping events and other non-JSON data
            if (!dataString.includes('"event":"ping"')) {
                 logger.warn("Could not parse Kimi SSE data chunk:", dataString);
            }
        }
    }
    
    logger.log("Kimi final reconstructed response:", fullText);
    const conversationId = currentConversationId;
    currentConversationId = null; // Reset for the next request
    
    // Construct the URL from the conversation ID
    const url = conversationId ? `https://www.kimi.com/chat/${conversationId}` : null;

    return { text: fullText, id: conversationId, url };
}
