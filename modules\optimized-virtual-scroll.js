/**
 * Optimized Virtual Scrolling Module
 * 
 * Enhanced virtual scrolling with better performance, dynamic item heights,
 * improved buffer management, and optimized rendering pipeline.
 */

import { createLogger } from './logger.js';
import { asyncDOMUpdater } from './async-dom-updater.js';

class OptimizedVirtualScroll {
    constructor(container, options = {}) {
        this.logger = createLogger(options.debugMode || false);
        this.container = container;
        
        // Configuration
        this.config = {
            itemHeight: options.itemHeight || 120,
            bufferSize: options.bufferSize || 8,
            overscan: options.overscan || 3,
            throttleMs: options.throttleMs || 16,
            dynamicHeight: options.dynamicHeight || false,
            ...options
        };
        
        // State
        this.items = [];
        this.visibleStart = 0;
        this.visibleEnd = 0;
        this.scrollTop = 0;
        this.containerHeight = 0;
        this.totalHeight = 0;
        
        // Dynamic height tracking
        this.itemHeights = new Map();
        this.averageItemHeight = this.config.itemHeight;
        this.measuredItems = new Set();
        
        // Performance optimization
        this.renderQueue = [];
        this.isRendering = false;
        this.lastRenderTime = 0;
        this.frameId = null;
        
        // DOM elements
        this.viewport = null;
        this.spacerTop = null;
        this.spacerBottom = null;
        this.content = null;
        
        // Event handlers
        this.scrollHandler = null;
        this.resizeHandler = null;
        this.intersectionObserver = null;
        
        // Performance metrics
        this.metrics = {
            renderCount: 0,
            averageRenderTime: 0,
            scrollEvents: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
        
        this.init();
    }

    /**
     * Initialize the virtual scroll container
     */
    init() {
        this.createDOMStructure();
        this.setupEventHandlers();
        this.setupIntersectionObserver();
        this.updateContainerHeight();
        
        this.logger.log('OptimizedVirtualScroll initialized', {
            itemHeight: this.config.itemHeight,
            bufferSize: this.config.bufferSize,
            dynamicHeight: this.config.dynamicHeight
        });
    }

    /**
     * Create DOM structure for virtual scrolling
     */
    createDOMStructure() {
        this.container.innerHTML = '';
        
        this.viewport = document.createElement('div');
        this.viewport.className = 'virtual-scroll-viewport h-full overflow-y-auto custom-scrollbar';
        this.viewport.style.position = 'relative';
        
        this.spacerTop = document.createElement('div');
        this.spacerTop.className = 'virtual-scroll-spacer-top';
        this.spacerTop.style.height = '0px';
        
        this.content = document.createElement('div');
        this.content.className = 'virtual-scroll-content';
        this.content.style.position = 'relative';
        
        this.spacerBottom = document.createElement('div');
        this.spacerBottom.className = 'virtual-scroll-spacer-bottom';
        this.spacerBottom.style.height = '0px';
        
        this.viewport.appendChild(this.spacerTop);
        this.viewport.appendChild(this.content);
        this.viewport.appendChild(this.spacerBottom);
        this.container.appendChild(this.viewport);
    }

    /**
     * Setup event handlers with throttling
     */
    setupEventHandlers() {
        this.scrollHandler = this.throttle(() => {
            this.handleScroll();
        }, this.config.throttleMs);
        
        this.resizeHandler = this.throttle(() => {
            this.updateContainerHeight();
            this.scheduleRender();
        }, 100);
        
        this.viewport.addEventListener('scroll', this.scrollHandler, { passive: true });
        window.addEventListener('resize', this.resizeHandler);
    }

    /**
     * Setup Intersection Observer for dynamic height measurement
     */
    setupIntersectionObserver() {
        if (!this.config.dynamicHeight) return;
        
        this.intersectionObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.measureItemHeight(entry.target);
                    }
                });
            },
            {
                root: this.viewport,
                rootMargin: '50px 0px',
                threshold: 0
            }
        );
    }

    /**
     * Set items for virtual scrolling
     * @param {Array} items - Array of items to display
     */
    setItems(items) {
        this.items = items;
        this.calculateTotalHeight();
        this.updateVisibleRange();
        this.scheduleRender();
        
        this.logger.log('Items set', { 
            itemCount: items.length, 
            totalHeight: this.totalHeight 
        });
    }

    /**
     * Calculate total height based on item heights
     */
    calculateTotalHeight() {
        if (this.config.dynamicHeight && this.itemHeights.size > 0) {
            this.totalHeight = 0;
            for (let i = 0; i < this.items.length; i++) {
                this.totalHeight += this.getItemHeight(i);
            }
        } else {
            this.totalHeight = this.items.length * this.config.itemHeight;
        }
    }

    /**
     * Get height for a specific item
     * @param {number} index - Item index
     * @returns {number} Item height
     */
    getItemHeight(index) {
        if (this.config.dynamicHeight && this.itemHeights.has(index)) {
            return this.itemHeights.get(index);
        }
        return this.averageItemHeight;
    }

    /**
     * Measure actual height of a rendered item
     * @param {HTMLElement} element - DOM element to measure
     */
    measureItemHeight(element) {
        const index = parseInt(element.dataset.index);
        if (isNaN(index) || this.measuredItems.has(index)) return;
        
        const height = element.offsetHeight;
        this.itemHeights.set(index, height);
        this.measuredItems.add(index);
        
        // Update average height
        const totalMeasured = this.itemHeights.size;
        const totalHeight = Array.from(this.itemHeights.values()).reduce((sum, h) => sum + h, 0);
        this.averageItemHeight = totalHeight / totalMeasured;
        
        // Recalculate if we have enough measurements
        if (totalMeasured % 10 === 0) {
            this.calculateTotalHeight();
            this.updateVisibleRange();
            this.scheduleRender();
        }
    }

    /**
     * Handle scroll events
     */
    handleScroll() {
        this.metrics.scrollEvents++;
        this.scrollTop = this.viewport.scrollTop;
        this.updateVisibleRange();
        this.scheduleRender();
    }

    /**
     * Update container height
     */
    updateContainerHeight() {
        if (this.viewport) {
            this.containerHeight = this.viewport.clientHeight;
        }
    }

    /**
     * Update visible range with improved buffer management
     */
    updateVisibleRange() {
        if (this.items.length === 0) {
            this.visibleStart = 0;
            this.visibleEnd = 0;
            return;
        }
        
        this.updateContainerHeight();
        
        if (this.config.dynamicHeight) {
            this.updateVisibleRangeDynamic();
        } else {
            this.updateVisibleRangeFixed();
        }
    }

    /**
     * Update visible range for fixed height items
     */
    updateVisibleRangeFixed() {
        const itemHeight = this.config.itemHeight;
        const visibleItemCount = Math.ceil(this.containerHeight / itemHeight);
        const bufferSize = this.config.bufferSize;
        
        this.visibleStart = Math.max(0, Math.floor(this.scrollTop / itemHeight) - bufferSize);
        this.visibleEnd = Math.min(
            this.items.length, 
            this.visibleStart + visibleItemCount + (bufferSize * 2)
        );
    }

    /**
     * Update visible range for dynamic height items
     */
    updateVisibleRangeDynamic() {
        let currentHeight = 0;
        let startIndex = 0;
        let endIndex = this.items.length;
        
        // Find start index
        for (let i = 0; i < this.items.length; i++) {
            const itemHeight = this.getItemHeight(i);
            if (currentHeight + itemHeight > this.scrollTop) {
                startIndex = Math.max(0, i - this.config.bufferSize);
                break;
            }
            currentHeight += itemHeight;
        }
        
        // Find end index
        currentHeight = 0;
        for (let i = startIndex; i < this.items.length; i++) {
            const itemHeight = this.getItemHeight(i);
            currentHeight += itemHeight;
            if (currentHeight > this.containerHeight + this.scrollTop) {
                endIndex = Math.min(this.items.length, i + this.config.bufferSize);
                break;
            }
        }
        
        this.visibleStart = startIndex;
        this.visibleEnd = endIndex;
    }

    /**
     * Schedule render with throttling
     */
    scheduleRender() {
        if (this.isRendering) return;
        
        this.isRendering = true;
        this.frameId = requestAnimationFrame(() => {
            this.render();
            this.isRendering = false;
        });
    }

    /**
     * Render visible items with optimization
     */
    render() {
        const startTime = performance.now();
        
        try {
            this.updateSpacers();
            this.renderItems();
            
            const renderTime = performance.now() - startTime;
            this.updateMetrics(renderTime);
            
        } catch (error) {
            this.logger.error('Render error:', error);
        }
    }

    /**
     * Update spacer heights
     */
    updateSpacers() {
        let topHeight = 0;
        let bottomHeight = 0;
        
        if (this.config.dynamicHeight) {
            // Calculate top spacer height
            for (let i = 0; i < this.visibleStart; i++) {
                topHeight += this.getItemHeight(i);
            }
            
            // Calculate bottom spacer height
            for (let i = this.visibleEnd; i < this.items.length; i++) {
                bottomHeight += this.getItemHeight(i);
            }
        } else {
            topHeight = this.visibleStart * this.config.itemHeight;
            bottomHeight = Math.max(0, (this.items.length - this.visibleEnd) * this.config.itemHeight);
        }
        
        this.spacerTop.style.height = `${topHeight}px`;
        this.spacerBottom.style.height = `${bottomHeight}px`;
    }

    /**
     * Render visible items
     */
    renderItems() {
        // Use async DOM updater for better performance
        asyncDOMUpdater.queueUpdate(() => {
            const fragment = document.createDocumentFragment();
            
            for (let i = this.visibleStart; i < this.visibleEnd; i++) {
                const item = this.items[i];
                if (item) {
                    const element = this.createItemElement(item, i);
                    fragment.appendChild(element);
                }
            }
            
            this.content.innerHTML = '';
            this.content.appendChild(fragment);
            
        }, 8, { visibleStart: this.visibleStart, visibleEnd: this.visibleEnd });
    }

    /**
     * Create item element (to be overridden by implementation)
     * @param {Object} item - Item data
     * @param {number} index - Item index
     * @returns {HTMLElement} Created element
     */
    createItemElement(item, index) {
        const element = document.createElement('div');
        element.className = 'virtual-scroll-item';
        element.dataset.index = index;
        element.textContent = `Item ${index}`;
        
        if (this.config.dynamicHeight && this.intersectionObserver) {
            this.intersectionObserver.observe(element);
        }
        
        return element;
    }

    /**
     * Update performance metrics
     * @param {number} renderTime - Render time in ms
     */
    updateMetrics(renderTime) {
        this.metrics.renderCount++;
        this.metrics.averageRenderTime = 
            (this.metrics.averageRenderTime * (this.metrics.renderCount - 1) + renderTime) / 
            this.metrics.renderCount;
    }

    /**
     * Throttle function execution
     * @param {Function} func - Function to throttle
     * @param {number} limit - Throttle limit in ms
     * @returns {Function} Throttled function
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Scroll to specific item
     * @param {number} index - Item index to scroll to
     */
    scrollToItem(index) {
        if (index < 0 || index >= this.items.length) return;
        
        let scrollTop = 0;
        
        if (this.config.dynamicHeight) {
            for (let i = 0; i < index; i++) {
                scrollTop += this.getItemHeight(i);
            }
        } else {
            scrollTop = index * this.config.itemHeight;
        }
        
        this.viewport.scrollTop = scrollTop;
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            visibleItems: this.visibleEnd - this.visibleStart,
            totalItems: this.items.length,
            averageItemHeight: this.averageItemHeight,
            measuredItems: this.measuredItems.size
        };
    }

    /**
     * Destroy virtual scroll and clean up
     */
    destroy() {
        if (this.frameId) {
            cancelAnimationFrame(this.frameId);
        }
        
        if (this.viewport && this.scrollHandler) {
            this.viewport.removeEventListener('scroll', this.scrollHandler);
        }
        
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        
        // Clean up DOM references
        this.viewport = null;
        this.spacerTop = null;
        this.spacerBottom = null;
        this.content = null;
        this.items = [];
        this.itemHeights.clear();
        this.measuredItems.clear();
        
        this.logger.log('OptimizedVirtualScroll destroyed');
    }
}

export { OptimizedVirtualScroll };
