/**
 * Platform Logic for <PERSON>yi Qi<PERSON>wen
 */

export const config = {
    name: '<PERSON><PERSON>',
    apiEndpoint: '/dialog/conversation',
};

export async function parseRequest(request, logger) {
    try {
        const requestBody = await request.clone().json();
        if (requestBody.contents && Array.isArray(requestBody.contents)) {
            const userMessage = requestBody.contents.find(m => m.role === 'user');
            if (userMessage && userMessage.content) {
                return userMessage.content;
            }
        }
    } catch (e) {
        logger.error("Error parsing request:", e);
    }
    return '';
}

export async function parseResponse(response, logger) {
    const sseStream = await response.clone().text();
    logger.log("Raw SSE Stream:", sseStream);
    const messages = sseStream.split('\n\n').filter(Boolean);
    let fullText = '';
    let sessionId = null;

    for (const messageBlock of messages) {
        const lines = messageBlock.split('\n');
        let dataString = null;
        for (const line of lines) {
            if (line.startsWith('data:')) {
                dataString = line.substring(5).trim();
                break;
            }
        }

        if (!dataString) continue;
        if (dataString === '[DONE]') break;

        try {
            const data = JSON.parse(dataString);
            logger.log("Parsed SSE data:", data);

            if (data.sessionId) {
                sessionId = data.sessionId;
            }

            if (data.contents && Array.isArray(data.contents) && data.contents.length > 0) {
                const content = data.contents[0];
                if (content && content.content && typeof content.content === 'string') {
                    if (data.msgStatus === 'finished') {
                        fullText = content.content;
                    }
                }
            }
        } catch (e) {
            logger.warn("Could not parse SSE data chunk:", dataString);
        }
    }
    
    logger.log("Final reconstructed response:", fullText);
    return { text: fullText, id: sessionId };
}