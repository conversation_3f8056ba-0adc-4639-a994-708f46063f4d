/**
 * Performance Integration Module
 * 
 * Integrates all performance optimizations for data processing efficiency
 * improvements in the LLMLog Chrome extension.
 */

import { performanceMonitor } from './performance-monitor.js';
import { searchOptimizer } from './search-optimizer.js';
import { progressiveSearch } from './progressive-search.js';
import { searchCache } from './search-cache.js';
import { asyncDOMUpdater } from './async-dom-updater.js';
import { OptimizedVirtualScroll } from './optimized-virtual-scroll.js';
import { createLogger } from './logger.js';

class PerformanceIntegration {
    constructor(debugMode = false) {
        this.logger = createLogger(debugMode);
        this.isInitialized = false;
        this.activeSearch = null;
        this.virtualScroll = null;
        
        // Performance configuration
        this.config = {
            enableProgressiveSearch: true,
            enableSearchCache: true,
            enableAsyncDOM: true,
            enableOptimizedVirtualScroll: true,
            enablePerformanceMonitoring: true,
            searchDebounceMs: 300,
            maxCacheSize: 200,
            virtualScrollBufferSize: 8
        };
        
        // Integration state
        this.state = {
            isSearching: false,
            lastSearchTerm: '',
            searchResults: [],
            totalResults: 0,
            searchTime: 0
        };
    }

    /**
     * Initialize performance integration
     * @param {HTMLElement} container - Main container element
     * @param {Object} options - Configuration options
     */
    async initialize(container, options = {}) {
        if (this.isInitialized) {
            this.logger.warn('Performance integration already initialized');
            return;
        }
        
        this.config = { ...this.config, ...options };
        this.container = container;
        
        try {
            // Initialize performance monitoring
            if (this.config.enablePerformanceMonitoring) {
                performanceMonitor.startTimer('performance_integration_init');
            }
            
            // Setup search cache
            if (this.config.enableSearchCache) {
                searchCache.maxCacheSize = this.config.maxCacheSize;
                this.logger.log('Search cache initialized', { maxSize: this.config.maxCacheSize });
            }
            
            // Setup optimized virtual scrolling
            if (this.config.enableOptimizedVirtualScroll) {
                this.initializeVirtualScroll();
            }
            
            // Setup cache invalidation listeners
            this.setupCacheInvalidation();
            
            // Setup performance monitoring
            this.setupPerformanceTracking();
            
            this.isInitialized = true;
            
            if (this.config.enablePerformanceMonitoring) {
                performanceMonitor.endTimer('performance_integration_init');
            }
            
            this.logger.log('Performance integration initialized successfully', this.config);
            
        } catch (error) {
            this.logger.error('Failed to initialize performance integration:', error);
            throw error;
        }
    }

    /**
     * Initialize optimized virtual scrolling
     */
    initializeVirtualScroll() {
        const scrollContainer = this.container.querySelector('#conversation-list') || this.container;
        
        this.virtualScroll = new OptimizedVirtualScroll(scrollContainer, {
            itemHeight: 120,
            bufferSize: this.config.virtualScrollBufferSize,
            dynamicHeight: false,
            debugMode: this.config.debugMode
        });
        
        // Override createItemElement to use our conversation template
        this.virtualScroll.createItemElement = (conversation, index) => {
            return this.createOptimizedConversationElement(conversation, index);
        };
        
        this.logger.log('Optimized virtual scroll initialized');
    }

    /**
     * Perform optimized search with all performance enhancements
     * @param {string} searchTerm - Search term
     * @param {Object} filters - Additional filters
     * @returns {Promise} Search promise
     */
    async performOptimizedSearch(searchTerm, filters = {}) {
        // Cancel previous search
        if (this.activeSearch) {
            progressiveSearch.cancelSearch(this.activeSearch);
        }
        
        this.state.isSearching = true;
        this.state.lastSearchTerm = searchTerm;
        
        const searchParams = {
            search: searchTerm,
            platform: filters.platform || '',
            page: 1,
            limit: 50,
            ...filters
        };
        
        try {
            // Start performance monitoring
            const timerId = performanceMonitor.startTimer('optimized_search', { searchTerm, filters });
            
            // Use progressive search for better UX
            if (this.config.enableProgressiveSearch) {
                this.activeSearch = await progressiveSearch.performProgressiveSearch(
                    searchParams,
                    (results, metadata) => this.handleProgressiveResults(results, metadata),
                    (results, metadata) => this.handleSearchComplete(results, metadata, timerId),
                    { priority: 8, streaming: true }
                );
            } else {
                // Fallback to direct search
                const results = await this.performDirectSearch(searchParams);
                this.handleSearchComplete(results, { searchId: 'direct' }, timerId);
            }
            
            return this.activeSearch;
            
        } catch (error) {
            this.logger.error('Optimized search failed:', error);
            this.state.isSearching = false;
            throw error;
        }
    }

    /**
     * Handle progressive search results
     * @param {Array} results - Partial results
     * @param {Object} metadata - Search metadata
     */
    handleProgressiveResults(results, metadata) {
        this.state.searchResults = results;
        this.state.totalResults = metadata.totalEstimate || results.length;
        
        // Update UI with progressive results
        if (this.config.enableAsyncDOM) {
            asyncDOMUpdater.queueUpdate(() => {
                this.updateSearchResultsUI(results, metadata);
            }, 8, { isPartial: metadata.isPartial });
        } else {
            this.updateSearchResultsUI(results, metadata);
        }
        
        this.logger.log('Progressive results received', {
            resultCount: results.length,
            isPartial: metadata.isPartial,
            fromCache: metadata.fromCache
        });
    }

    /**
     * Handle search completion
     * @param {Array} results - Final results
     * @param {Object} metadata - Search metadata
     * @param {string} timerId - Performance timer ID
     */
    handleSearchComplete(results, metadata, timerId) {
        this.state.isSearching = false;
        this.state.searchResults = results;
        this.state.totalResults = results.length;
        this.state.searchTime = metadata.searchTime || 0;
        
        // End performance monitoring
        if (timerId) {
            performanceMonitor.endTimer(timerId, {
                resultCount: results.length,
                fromCache: metadata.fromCache,
                streaming: metadata.streaming
            });
        }
        
        // Final UI update
        if (this.config.enableAsyncDOM) {
            asyncDOMUpdater.queueUpdate(() => {
                this.updateSearchResultsUI(results, { ...metadata, isComplete: true });
            }, 9);
        } else {
            this.updateSearchResultsUI(results, { ...metadata, isComplete: true });
        }
        
        this.logger.log('Search completed', {
            resultCount: results.length,
            searchTime: this.state.searchTime,
            fromCache: metadata.fromCache
        });
    }

    /**
     * Update search results UI
     * @param {Array} results - Search results
     * @param {Object} metadata - Metadata
     */
    updateSearchResultsUI(results, metadata) {
        if (this.virtualScroll) {
            // Use optimized virtual scrolling
            this.virtualScroll.setItems(results);
        } else {
            // Fallback to direct DOM manipulation
            this.renderResultsDirectly(results, metadata);
        }
        
        // Update search status
        this.updateSearchStatus(results.length, metadata);
    }

    /**
     * Create optimized conversation element
     * @param {Object} conversation - Conversation data
     * @param {number} index - Item index
     * @returns {HTMLElement} Conversation element
     */
    createOptimizedConversationElement(conversation, index) {
        const element = document.createElement('div');
        element.className = 'conversation-item p-3 mb-2 bg-white rounded-lg shadow-sm border border-slate-200 hover:shadow-md transition-shadow cursor-pointer';
        element.dataset.index = index;
        element.dataset.id = conversation.id;
        
        // Use efficient innerHTML for better performance
        const dateString = new Date(conversation.createdAt).toLocaleDateString();
        const platformColor = this.getPlatformColor(conversation.platform);
        
        element.innerHTML = `
            <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-2 mb-1">
                        <span class="inline-block w-2 h-2 rounded-full ${platformColor}"></span>
                        <span class="text-xs font-medium text-slate-600 uppercase">${conversation.platform}</span>
                        <span class="text-xs text-slate-400">${dateString}</span>
                    </div>
                    <h3 class="text-sm font-semibold text-slate-800 truncate mb-1">
                        ${this.escapeHTML(conversation.title || 'Untitled')}
                    </h3>
                    <p class="text-xs text-slate-600 line-clamp-2">
                        ${this.escapeHTML((conversation.prompt || '').substring(0, 120))}...
                    </p>
                </div>
                <button class="option-button ml-2 p-1 text-slate-400 hover:text-red-500 transition-colors" 
                        aria-label="Delete conversation">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        // Add event listeners
        element.addEventListener('click', (e) => {
            if (!e.target.closest('.option-button')) {
                this.handleConversationClick(conversation);
            }
        });
        
        const deleteButton = element.querySelector('.option-button');
        deleteButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.handleConversationDelete(conversation.id);
        });
        
        return element;
    }

    /**
     * Get platform color class
     * @param {string} platform - Platform name
     * @returns {string} Color class
     */
    getPlatformColor(platform) {
        const colors = {
            chatgpt: 'bg-green-500',
            claude: 'bg-orange-500',
            gemini: 'bg-blue-500',
            default: 'bg-slate-500'
        };
        return colors[platform?.toLowerCase()] || colors.default;
    }

    /**
     * Escape HTML to prevent XSS
     * @param {string} str - String to escape
     * @returns {string} Escaped string
     */
    escapeHTML(str) {
        if (!str) return '';
        const div = document.createElement('div');
        div.textContent = str;
        return div.innerHTML;
    }

    /**
     * Setup cache invalidation listeners
     */
    setupCacheInvalidation() {
        // Listen for conversation changes
        window.addEventListener('llmlog-conversation-added', () => {
            searchCache.invalidate('conversation_added');
        });
        
        window.addEventListener('llmlog-conversation-deleted', (e) => {
            searchCache.invalidate('conversation_deleted', { conversationId: e.detail.id });
        });
        
        window.addEventListener('llmlog-conversation-updated', (e) => {
            searchCache.invalidate('conversation_updated', { conversationId: e.detail.id });
        });
    }

    /**
     * Setup performance tracking
     */
    setupPerformanceTracking() {
        // Track search performance
        window.addEventListener('llmlog-search-started', (e) => {
            performanceMonitor.recordMetric('search_started', {
                searchTerm: e.detail.searchTerm,
                timestamp: Date.now()
            });
        });
        
        window.addEventListener('llmlog-search-completed', (e) => {
            performanceMonitor.recordMetric('search_completed', {
                searchTerm: e.detail.searchTerm,
                resultCount: e.detail.resultCount,
                duration: e.detail.duration,
                fromCache: e.detail.fromCache
            });
        });
    }

    /**
     * Handle conversation click
     * @param {Object} conversation - Conversation data
     */
    handleConversationClick(conversation) {
        // Dispatch event for main app to handle
        window.dispatchEvent(new CustomEvent('llmlog-conversation-selected', {
            detail: { conversation }
        }));
    }

    /**
     * Handle conversation deletion
     * @param {number} conversationId - Conversation ID
     */
    handleConversationDelete(conversationId) {
        // Dispatch event for main app to handle
        window.dispatchEvent(new CustomEvent('llmlog-conversation-delete', {
            detail: { id: conversationId }
        }));
    }

    /**
     * Update search status display
     * @param {number} resultCount - Number of results
     * @param {Object} metadata - Search metadata
     */
    updateSearchStatus(resultCount, metadata) {
        const statusElement = document.getElementById('search-status');
        if (statusElement) {
            let statusText = `${resultCount} result${resultCount !== 1 ? 's' : ''}`;
            
            if (metadata.isPartial) {
                statusText += ' (loading...)';
            }
            
            if (metadata.fromCache) {
                statusText += ' (cached)';
            }
            
            statusElement.textContent = statusText;
        }
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getPerformanceMetrics() {
        return {
            integration: {
                isInitialized: this.isInitialized,
                activeSearch: !!this.activeSearch,
                searchState: this.state
            },
            monitor: performanceMonitor.getPerformanceSummary(),
            cache: searchCache.getStats(),
            virtualScroll: this.virtualScroll?.getMetrics(),
            asyncDOM: asyncDOMUpdater.getMetrics(),
            progressiveSearch: progressiveSearch.getMetrics()
        };
    }

    /**
     * Destroy performance integration
     */
    destroy() {
        if (this.activeSearch) {
            progressiveSearch.cancelSearch(this.activeSearch);
        }
        
        if (this.virtualScroll) {
            this.virtualScroll.destroy();
        }
        
        asyncDOMUpdater.destroy();
        performanceMonitor.destroy();
        
        this.isInitialized = false;
        this.logger.log('Performance integration destroyed');
    }
}

// Export singleton instance
export const performanceIntegration = new PerformanceIntegration();
export { PerformanceIntegration };
