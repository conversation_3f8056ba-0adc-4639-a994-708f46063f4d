/**
 * Search Optimizer Module
 * 
 * Advanced search functionality with ranking, caching, and performance optimization
 * for the LLMLog Chrome extension.
 */

import { createLogger } from './logger.js';

class SearchOptimizer {
    constructor(debugMode = false) {
        this.logger = createLogger(debugMode);
        this.searchCache = new Map();
        this.maxCacheSize = 100;
        this.cacheHits = 0;
        this.cacheMisses = 0;
        
        // Search performance metrics
        this.searchMetrics = {
            totalSearches: 0,
            averageSearchTime: 0,
            slowSearchThreshold: 100 // ms
        };
    }

    /**
     * Perform optimized search with caching and ranking
     * @param {string} searchTerm - The search term
     * @param {Array} conversations - Array of conversations to search
     * @param {Object} options - Search options
     * @returns {Array} Ranked search results
     */
    async performOptimizedSearch(searchTerm, conversations, options = {}) {
        const startTime = performance.now();
        
        try {
            // Check cache first
            const cacheKey = this.generateCacheKey(searchTerm, options);
            if (this.searchCache.has(cacheKey)) {
                this.cacheHits++;
                this.logger.log('Search cache hit', { searchTerm, cacheKey });
                return this.searchCache.get(cacheKey);
            }
            
            this.cacheMisses++;
            
            // Perform search with ranking
            const results = await this.searchWithRanking(searchTerm, conversations, options);
            
            // Cache results
            this.cacheSearchResults(cacheKey, results);
            
            // Update metrics
            const searchTime = performance.now() - startTime;
            this.updateSearchMetrics(searchTime);
            
            if (searchTime > this.searchMetrics.slowSearchThreshold) {
                this.logger.warn('Slow search detected', { 
                    searchTerm, 
                    searchTime: `${searchTime.toFixed(2)}ms`,
                    resultCount: results.length 
                });
            }
            
            return results;
            
        } catch (error) {
            this.logger.error('Search optimization error:', error);
            throw error;
        }
    }

    /**
     * Search with ranking algorithm
     * @param {string} searchTerm - The search term
     * @param {Array} conversations - Array of conversations to search
     * @param {Object} options - Search options
     * @returns {Array} Ranked search results
     */
    async searchWithRanking(searchTerm, conversations, options = {}) {
        const searchTerms = this.preprocessSearchTerm(searchTerm);
        const results = [];
        
        for (const conversation of conversations) {
            const score = this.calculateRelevanceScore(conversation, searchTerms, options);
            if (score > 0) {
                results.push({
                    ...conversation,
                    _searchScore: score,
                    _matchedFields: this.getMatchedFields(conversation, searchTerms)
                });
            }
        }
        
        // Sort by relevance score (highest first)
        return results.sort((a, b) => b._searchScore - a._searchScore);
    }

    /**
     * Preprocess search term for better matching
     * @param {string} searchTerm - Raw search term
     * @returns {Array} Processed search terms
     */
    preprocessSearchTerm(searchTerm) {
        return searchTerm
            .toLowerCase()
            .trim()
            .split(/\s+/)
            .filter(term => term.length > 0)
            .map(term => term.replace(/[^\w\s]/g, '')); // Remove special characters
    }

    /**
     * Calculate relevance score for a conversation
     * @param {Object} conversation - Conversation object
     * @param {Array} searchTerms - Preprocessed search terms
     * @param {Object} options - Search options
     * @returns {number} Relevance score
     */
    calculateRelevanceScore(conversation, searchTerms, options = {}) {
        let score = 0;
        const weights = {
            title: 3.0,      // Title matches are most important
            platform: 2.5,   // Platform matches are highly relevant
            prompt: 2.0,     // User prompts are important
            response: 1.0,   // Response content is baseline
            url: 1.5         // URL matches are moderately important
        };

        // Boost recent conversations
        const ageBoost = this.calculateAgeBoost(conversation.createdAt);
        
        for (const term of searchTerms) {
            // Title matching
            if (conversation.title) {
                const titleMatches = this.countMatches(conversation.title.toLowerCase(), term);
                score += titleMatches * weights.title;
            }
            
            // Platform matching
            if (conversation.platform) {
                const platformMatches = this.countMatches(conversation.platform.toLowerCase(), term);
                score += platformMatches * weights.platform;
            }
            
            // Prompt matching
            if (conversation.prompt) {
                const promptMatches = this.countMatches(conversation.prompt.toLowerCase(), term);
                score += promptMatches * weights.prompt;
            }
            
            // Response matching
            if (conversation.response) {
                const responseMatches = this.countMatches(conversation.response.toLowerCase(), term);
                score += responseMatches * weights.response;
            }
            
            // URL matching
            if (conversation.url) {
                const urlMatches = this.countMatches(conversation.url.toLowerCase(), term);
                score += urlMatches * weights.url;
            }
        }
        
        // Apply age boost
        score *= ageBoost;
        
        // Apply platform filter boost if specified
        if (options.platform && conversation.platform === options.platform) {
            score *= 1.5;
        }
        
        return score;
    }

    /**
     * Count occurrences of a term in text
     * @param {string} text - Text to search in
     * @param {string} term - Term to search for
     * @returns {number} Number of matches
     */
    countMatches(text, term) {
        if (!text || !term) return 0;
        
        // Exact word matches get higher score
        const exactMatches = (text.match(new RegExp(`\\b${term}\\b`, 'g')) || []).length;
        const partialMatches = (text.match(new RegExp(term, 'g')) || []).length - exactMatches;
        
        return exactMatches * 2 + partialMatches;
    }

    /**
     * Calculate age boost for recent conversations
     * @param {string} createdAt - Creation timestamp
     * @returns {number} Age boost multiplier
     */
    calculateAgeBoost(createdAt) {
        const now = Date.now();
        const created = new Date(createdAt).getTime();
        const ageInDays = (now - created) / (1000 * 60 * 60 * 24);
        
        // Boost recent conversations (within 7 days)
        if (ageInDays <= 1) return 1.3;      // Last day: 30% boost
        if (ageInDays <= 7) return 1.1;      // Last week: 10% boost
        if (ageInDays <= 30) return 1.0;     // Last month: no boost
        return 0.9;                          // Older: slight penalty
    }

    /**
     * Get fields that matched the search terms
     * @param {Object} conversation - Conversation object
     * @param {Array} searchTerms - Search terms
     * @returns {Array} Array of matched field names
     */
    getMatchedFields(conversation, searchTerms) {
        const matchedFields = [];
        
        for (const term of searchTerms) {
            if (conversation.title && conversation.title.toLowerCase().includes(term)) {
                matchedFields.push('title');
            }
            if (conversation.platform && conversation.platform.toLowerCase().includes(term)) {
                matchedFields.push('platform');
            }
            if (conversation.prompt && conversation.prompt.toLowerCase().includes(term)) {
                matchedFields.push('prompt');
            }
            if (conversation.response && conversation.response.toLowerCase().includes(term)) {
                matchedFields.push('response');
            }
            if (conversation.url && conversation.url.toLowerCase().includes(term)) {
                matchedFields.push('url');
            }
        }
        
        return [...new Set(matchedFields)]; // Remove duplicates
    }

    /**
     * Generate cache key for search results
     * @param {string} searchTerm - Search term
     * @param {Object} options - Search options
     * @returns {string} Cache key
     */
    generateCacheKey(searchTerm, options) {
        const optionsStr = JSON.stringify(options);
        return `${searchTerm.toLowerCase().trim()}|${optionsStr}`;
    }

    /**
     * Cache search results with LRU eviction
     * @param {string} cacheKey - Cache key
     * @param {Array} results - Search results
     */
    cacheSearchResults(cacheKey, results) {
        // Implement LRU eviction
        if (this.searchCache.size >= this.maxCacheSize) {
            const firstKey = this.searchCache.keys().next().value;
            this.searchCache.delete(firstKey);
        }
        
        this.searchCache.set(cacheKey, results);
        this.logger.log('Search results cached', { 
            cacheKey, 
            resultCount: results.length,
            cacheSize: this.searchCache.size 
        });
    }

    /**
     * Update search performance metrics
     * @param {number} searchTime - Search execution time in ms
     */
    updateSearchMetrics(searchTime) {
        this.searchMetrics.totalSearches++;
        this.searchMetrics.averageSearchTime = 
            (this.searchMetrics.averageSearchTime * (this.searchMetrics.totalSearches - 1) + searchTime) / 
            this.searchMetrics.totalSearches;
    }

    /**
     * Clear search cache
     */
    clearCache() {
        this.searchCache.clear();
        this.cacheHits = 0;
        this.cacheMisses = 0;
        this.logger.log('Search cache cleared');
    }

    /**
     * Get search performance statistics
     * @returns {Object} Performance statistics
     */
    getPerformanceStats() {
        return {
            ...this.searchMetrics,
            cacheHitRate: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0,
            cacheSize: this.searchCache.size,
            maxCacheSize: this.maxCacheSize
        };
    }
}

// Export singleton instance
export const searchOptimizer = new SearchOptimizer();
export { SearchOptimizer };
