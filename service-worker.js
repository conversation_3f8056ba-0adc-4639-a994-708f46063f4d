import { initialize as initializeRouter } from './modules/router.js';
import { createLogger } from './modules/logger.js';
import { getSetting } from './modules/settings.js';

let logger;

// Keep the service worker alive
chrome.alarms.onAlarm.addListener(alarm => {
    if (alarm.name === 'keep-alive') {
        // This listener itself is enough to keep the service worker alive.
        // You can add a log here for debugging if needed.
        // logger?.log('Keep-alive alarm triggered.');
    }
});

// Initialize core modules with memory optimization
async function main() {
    const debugLoggingEnabled = await getSetting('debugLoggingEnabled');
    logger = createLogger(debugLoggingEnabled);

    // Setup the keep-alive alarm with optimized timing
    chrome.alarms.create('keep-alive', {
        delayInMinutes: 0.1, // Start after 6 seconds
        periodInMinutes: 0.33 // Trigger every 20 seconds
    });

    // Handle connections from content scripts with cleanup
    chrome.runtime.onConnect.addListener(port => {
        logger.log(`Connection established from ${port.name}`);

        // Set up cleanup when port disconnects
        port.onDisconnect.addListener(() => {
            logger.log(`Port ${port.name} disconnected.`);
            // Trigger garbage collection hint if available
            if (typeof gc === 'function') {
                setTimeout(() => {
                    try {
                        gc();
                    } catch (e) {
                        // Ignore errors
                    }
                }, 1000);
            }
        });
    });

    try {
        await initializeRouter();
        logger.log("LLMLog Service Worker initialized successfully.");

        // Start memory monitoring if available
        try {
            const { getMemoryMonitor } = await import('./modules/memory-monitor.js');
            const memoryMonitor = getMemoryMonitor(debugLoggingEnabled);
            memoryMonitor.startMonitoring(120000); // Check every 2 minutes for service worker
        } catch (error) {
            logger.log('Memory monitoring not available in service worker');
        }
    } catch (e) {
        logger.error("Failed to initialize Service Worker:", e);
    }
}

main();
