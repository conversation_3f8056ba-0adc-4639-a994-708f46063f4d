/**
 * Performance Monitor Module
 * 
 * Comprehensive performance monitoring for search operations, DOM updates,
 * database queries, and overall application performance.
 */

import { createLogger } from './logger.js';

class PerformanceMonitor {
    constructor(debugMode = false) {
        this.logger = createLogger(debugMode);
        this.isMonitoring = false;
        this.metrics = new Map();
        this.timers = new Map();
        this.observers = new Map();
        
        // Performance thresholds (in milliseconds)
        this.thresholds = {
            search: 200,        // Search operations
            domUpdate: 16,      // DOM updates (60fps)
            dbQuery: 100,       // Database queries
            render: 16,         // Rendering operations
            memory: 50 * 1024 * 1024  // 50MB memory usage
        };
        
        // Monitoring configuration
        this.config = {
            sampleRate: 1.0,           // Sample 100% of operations
            maxSamples: 1000,          // Keep last 1000 samples per metric
            reportInterval: 30000,     // Report every 30 seconds
            alertThreshold: 0.1        // Alert if 10% of operations are slow
        };
        
        // Performance data
        this.performanceData = {
            search: [],
            domUpdate: [],
            dbQuery: [],
            render: [],
            memory: [],
            userInteraction: []
        };
        
        // Real-time statistics
        this.stats = {
            totalOperations: 0,
            slowOperations: 0,
            averageResponseTime: 0,
            memoryUsage: 0,
            frameRate: 0
        };
        
        this.initializeMonitoring();
    }

    /**
     * Initialize performance monitoring
     */
    initializeMonitoring() {
        this.setupPerformanceObserver();
        this.setupMemoryMonitoring();
        this.setupFrameRateMonitoring();
        this.startPeriodicReporting();
        
        this.logger.log('Performance monitoring initialized', this.config);
    }

    /**
     * Setup Performance Observer for browser metrics
     */
    setupPerformanceObserver() {
        if (!window.PerformanceObserver) {
            this.logger.warn('PerformanceObserver not available');
            return;
        }
        
        try {
            // Observe navigation timing
            const navObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric('navigation', {
                        type: entry.entryType,
                        name: entry.name,
                        duration: entry.duration,
                        startTime: entry.startTime
                    });
                }
            });
            navObserver.observe({ entryTypes: ['navigation'] });
            
            // Observe resource timing
            const resourceObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name.includes('chrome-extension://')) {
                        this.recordMetric('resource', {
                            name: entry.name,
                            duration: entry.duration,
                            transferSize: entry.transferSize || 0
                        });
                    }
                }
            });
            resourceObserver.observe({ entryTypes: ['resource'] });
            
            // Observe user timing
            const userObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.recordMetric('userTiming', {
                        name: entry.name,
                        duration: entry.duration,
                        detail: entry.detail
                    });
                }
            });
            userObserver.observe({ entryTypes: ['measure', 'mark'] });
            
            this.observers.set('navigation', navObserver);
            this.observers.set('resource', resourceObserver);
            this.observers.set('userTiming', userObserver);
            
        } catch (error) {
            this.logger.error('Failed to setup PerformanceObserver:', error);
        }
    }

    /**
     * Setup memory monitoring
     */
    setupMemoryMonitoring() {
        if (!performance.memory) {
            this.logger.warn('Memory monitoring not available');
            return;
        }
        
        setInterval(() => {
            const memInfo = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };
            
            this.recordMetric('memory', memInfo);
            this.stats.memoryUsage = memInfo.used;
            
            // Check memory threshold
            if (memInfo.used > this.thresholds.memory) {
                this.alertSlowOperation('memory', memInfo.used, this.thresholds.memory);
            }
            
        }, 5000); // Check every 5 seconds
    }

    /**
     * Setup frame rate monitoring
     */
    setupFrameRateMonitoring() {
        let frameCount = 0;
        let lastTime = performance.now();
        
        const countFrame = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                this.stats.frameRate = frameCount;
                this.recordMetric('frameRate', {
                    fps: frameCount,
                    timestamp: currentTime
                });
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(countFrame);
        };
        
        requestAnimationFrame(countFrame);
    }

    /**
     * Start a performance timer
     * @param {string} operation - Operation name
     * @param {Object} context - Additional context
     * @returns {string} Timer ID
     */
    startTimer(operation, context = {}) {
        const timerId = `${operation}_${Date.now()}_${Math.random()}`;
        
        this.timers.set(timerId, {
            operation,
            startTime: performance.now(),
            context
        });
        
        // Create performance mark
        performance.mark(`${operation}-start-${timerId}`);
        
        return timerId;
    }

    /**
     * End a performance timer
     * @param {string} timerId - Timer ID
     * @param {Object} additionalContext - Additional context
     */
    endTimer(timerId, additionalContext = {}) {
        const timer = this.timers.get(timerId);
        if (!timer) {
            this.logger.warn('Timer not found:', timerId);
            return;
        }
        
        const endTime = performance.now();
        const duration = endTime - timer.startTime;
        
        // Create performance measure
        performance.mark(`${timer.operation}-end-${timerId}`);
        performance.measure(
            `${timer.operation}-${timerId}`,
            `${timer.operation}-start-${timerId}`,
            `${timer.operation}-end-${timerId}`
        );
        
        // Record metric
        this.recordMetric(timer.operation, {
            duration,
            startTime: timer.startTime,
            endTime,
            context: { ...timer.context, ...additionalContext }
        });
        
        // Check threshold
        const threshold = this.thresholds[timer.operation];
        if (threshold && duration > threshold) {
            this.alertSlowOperation(timer.operation, duration, threshold);
        }
        
        this.timers.delete(timerId);
        
        this.logger.log(`${timer.operation} completed`, {
            duration: `${duration.toFixed(2)}ms`,
            timerId
        });
    }

    /**
     * Record a performance metric
     * @param {string} category - Metric category
     * @param {Object} data - Metric data
     */
    recordMetric(category, data) {
        if (Math.random() > this.config.sampleRate) {
            return; // Skip based on sample rate
        }
        
        if (!this.performanceData[category]) {
            this.performanceData[category] = [];
        }
        
        const metric = {
            ...data,
            timestamp: data.timestamp || Date.now()
        };
        
        this.performanceData[category].push(metric);
        
        // Limit samples to prevent memory growth
        if (this.performanceData[category].length > this.config.maxSamples) {
            this.performanceData[category].shift();
        }
        
        this.updateStats(category, data);
    }

    /**
     * Update real-time statistics
     * @param {string} category - Metric category
     * @param {Object} data - Metric data
     */
    updateStats(category, data) {
        this.stats.totalOperations++;
        
        if (data.duration) {
            // Update average response time
            this.stats.averageResponseTime = 
                (this.stats.averageResponseTime * (this.stats.totalOperations - 1) + data.duration) / 
                this.stats.totalOperations;
        }
    }

    /**
     * Alert for slow operations
     * @param {string} operation - Operation type
     * @param {number} actualTime - Actual execution time
     * @param {number} threshold - Threshold time
     */
    alertSlowOperation(operation, actualTime, threshold) {
        this.stats.slowOperations++;
        
        const slowRatio = this.stats.slowOperations / this.stats.totalOperations;
        
        this.logger.warn('Slow operation detected', {
            operation,
            actualTime: `${actualTime.toFixed(2)}ms`,
            threshold: `${threshold}ms`,
            slowRatio: `${(slowRatio * 100).toFixed(1)}%`
        });
        
        // Alert if too many slow operations
        if (slowRatio > this.config.alertThreshold) {
            this.logger.error('Performance degradation detected', {
                slowOperationRatio: `${(slowRatio * 100).toFixed(1)}%`,
                totalOperations: this.stats.totalOperations
            });
        }
    }

    /**
     * Get performance summary
     * @param {string} category - Optional category filter
     * @returns {Object} Performance summary
     */
    getPerformanceSummary(category = null) {
        const summary = {
            timestamp: Date.now(),
            stats: { ...this.stats },
            categories: {}
        };
        
        const categories = category ? [category] : Object.keys(this.performanceData);
        
        for (const cat of categories) {
            const data = this.performanceData[cat] || [];
            if (data.length === 0) continue;
            
            const durations = data
                .filter(d => d.duration !== undefined)
                .map(d => d.duration);
            
            if (durations.length > 0) {
                summary.categories[cat] = {
                    count: data.length,
                    average: durations.reduce((a, b) => a + b, 0) / durations.length,
                    min: Math.min(...durations),
                    max: Math.max(...durations),
                    p95: this.calculatePercentile(durations, 95),
                    p99: this.calculatePercentile(durations, 99)
                };
            }
        }
        
        return summary;
    }

    /**
     * Calculate percentile value
     * @param {Array} values - Array of values
     * @param {number} percentile - Percentile (0-100)
     * @returns {number} Percentile value
     */
    calculatePercentile(values, percentile) {
        const sorted = [...values].sort((a, b) => a - b);
        const index = Math.ceil((percentile / 100) * sorted.length) - 1;
        return sorted[Math.max(0, index)];
    }

    /**
     * Start periodic performance reporting
     */
    startPeriodicReporting() {
        setInterval(() => {
            const summary = this.getPerformanceSummary();
            this.logger.log('Performance Report', summary);
            
            // Reset some counters
            this.stats.slowOperations = Math.floor(this.stats.slowOperations * 0.9);
            
        }, this.config.reportInterval);
    }

    /**
     * Export performance data
     * @param {string} format - Export format ('json' or 'csv')
     * @returns {string} Exported data
     */
    exportData(format = 'json') {
        const data = {
            timestamp: Date.now(),
            config: this.config,
            stats: this.stats,
            performanceData: this.performanceData
        };
        
        if (format === 'json') {
            return JSON.stringify(data, null, 2);
        } else if (format === 'csv') {
            // Simple CSV export for metrics
            let csv = 'Category,Timestamp,Duration,Context\n';
            
            for (const [category, metrics] of Object.entries(this.performanceData)) {
                for (const metric of metrics) {
                    csv += `${category},${metric.timestamp},${metric.duration || ''},${JSON.stringify(metric.context || {})}\n`;
                }
            }
            
            return csv;
        }
        
        return JSON.stringify(data);
    }

    /**
     * Clear performance data
     * @param {string} category - Optional category to clear
     */
    clearData(category = null) {
        if (category) {
            this.performanceData[category] = [];
        } else {
            for (const cat of Object.keys(this.performanceData)) {
                this.performanceData[cat] = [];
            }
            this.stats = {
                totalOperations: 0,
                slowOperations: 0,
                averageResponseTime: 0,
                memoryUsage: this.stats.memoryUsage,
                frameRate: this.stats.frameRate
            };
        }
        
        this.logger.log('Performance data cleared', { category });
    }

    /**
     * Destroy performance monitor
     */
    destroy() {
        // Disconnect observers
        for (const [name, observer] of this.observers) {
            try {
                observer.disconnect();
            } catch (error) {
                this.logger.error(`Failed to disconnect ${name} observer:`, error);
            }
        }
        
        this.observers.clear();
        this.timers.clear();
        this.metrics.clear();
        
        this.logger.log('Performance monitor destroyed');
    }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();
export { PerformanceMonitor };
