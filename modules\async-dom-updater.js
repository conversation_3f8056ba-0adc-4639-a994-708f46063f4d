/**
 * Asynchronous DOM Updater Module
 * 
 * Provides batched, asynchronous DOM updates using requestAnimationFrame
 * and Web Workers for heavy processing to improve UI responsiveness.
 */

import { createLogger } from './logger.js';

class AsyncDOMUpdater {
    constructor(debugMode = false) {
        this.logger = createLogger(debugMode);
        this.updateQueue = [];
        this.isProcessing = false;
        this.batchSize = 10; // Process 10 updates per frame
        this.frameId = null;
        
        // Performance tracking
        this.metrics = {
            totalUpdates: 0,
            batchedUpdates: 0,
            averageFrameTime: 0,
            droppedFrames: 0
        };
        
        // Web Worker for heavy processing (if available)
        this.worker = null;
        this.initializeWorker();
    }

    /**
     * Initialize Web Worker for heavy DOM processing
     */
    initializeWorker() {
        try {
            // Create inline worker for text processing
            const workerScript = `
                self.onmessage = function(e) {
                    const { type, data } = e.data;
                    
                    switch(type) {
                        case 'processSearchResults':
                            const processed = processSearchResults(data);
                            self.postMessage({ type: 'searchResultsProcessed', data: processed });
                            break;
                        case 'generateHTML':
                            const html = generateConversationHTML(data);
                            self.postMessage({ type: 'htmlGenerated', data: html });
                            break;
                    }
                };
                
                function processSearchResults(conversations) {
                    return conversations.map(conv => ({
                        ...conv,
                        processedTitle: conv.title ? conv.title.substring(0, 100) : 'Untitled',
                        processedPrompt: conv.prompt ? conv.prompt.substring(0, 200) : '',
                        processedResponse: conv.response ? conv.response.substring(0, 300) : ''
                    }));
                }
                
                function generateConversationHTML(conversations) {
                    return conversations.map(conv => {
                        const title = escapeHTML(conv.processedTitle || 'Untitled');
                        const platform = escapeHTML(conv.platform || 'Unknown');
                        const date = new Date(conv.createdAt).toLocaleDateString();
                        
                        return {
                            id: conv.id,
                            html: \`
                                <div class="conversation-item" data-id="\${conv.id}">
                                    <h3>\${title}</h3>
                                    <p class="platform">\${platform}</p>
                                    <p class="date">\${date}</p>
                                </div>
                            \`
                        };
                    });
                }
                
                function escapeHTML(str) {
                    const div = document.createElement('div');
                    div.textContent = str;
                    return div.innerHTML;
                }
            `;
            
            const blob = new Blob([workerScript], { type: 'application/javascript' });
            this.worker = new Worker(URL.createObjectURL(blob));
            
            this.worker.onmessage = (e) => {
                this.handleWorkerMessage(e.data);
            };
            
            this.worker.onerror = (error) => {
                this.logger.error('Web Worker error:', error);
                this.worker = null;
            };
            
            this.logger.log('Web Worker initialized for heavy DOM processing');
            
        } catch (error) {
            this.logger.warn('Web Worker not available, falling back to main thread:', error);
            this.worker = null;
        }
    }

    /**
     * Handle messages from Web Worker
     * @param {Object} message - Worker message
     */
    handleWorkerMessage(message) {
        const { type, data } = message;
        
        switch (type) {
            case 'searchResultsProcessed':
                this.applyProcessedSearchResults(data);
                break;
            case 'htmlGenerated':
                this.applyGeneratedHTML(data);
                break;
        }
    }

    /**
     * Queue a DOM update for batched processing
     * @param {Function} updateFunction - Function to execute for DOM update
     * @param {number} priority - Update priority (1-10, higher = more important)
     * @param {Object} context - Context data for the update
     */
    queueUpdate(updateFunction, priority = 5, context = {}) {
        const update = {
            id: Date.now() + Math.random(),
            function: updateFunction,
            priority,
            context,
            timestamp: performance.now()
        };
        
        this.updateQueue.push(update);
        
        // Sort by priority (higher priority first)
        this.updateQueue.sort((a, b) => b.priority - a.priority);
        
        this.scheduleProcessing();
        
        this.logger.log('DOM update queued', { 
            updateId: update.id, 
            priority, 
            queueLength: this.updateQueue.length 
        });
    }

    /**
     * Schedule processing of queued updates
     */
    scheduleProcessing() {
        if (this.isProcessing || this.updateQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        this.frameId = requestAnimationFrame(() => this.processUpdateBatch());
    }

    /**
     * Process a batch of DOM updates
     */
    processUpdateBatch() {
        const startTime = performance.now();
        const maxFrameTime = 16; // Target 60fps (16ms per frame)
        
        let processedCount = 0;
        
        while (this.updateQueue.length > 0 && processedCount < this.batchSize) {
            const frameTime = performance.now() - startTime;
            
            // If we're approaching frame budget, defer remaining updates
            if (frameTime > maxFrameTime * 0.8) {
                this.metrics.droppedFrames++;
                break;
            }
            
            const update = this.updateQueue.shift();
            
            try {
                update.function(update.context);
                processedCount++;
                this.metrics.totalUpdates++;
                
            } catch (error) {
                this.logger.error('DOM update error:', error, { updateId: update.id });
            }
        }
        
        const frameTime = performance.now() - startTime;
        this.updateFrameMetrics(frameTime);
        
        this.logger.log('DOM batch processed', { 
            processedCount, 
            frameTime: `${frameTime.toFixed(2)}ms`,
            remainingUpdates: this.updateQueue.length 
        });
        
        // Schedule next batch if there are more updates
        if (this.updateQueue.length > 0) {
            this.frameId = requestAnimationFrame(() => this.processUpdateBatch());
        } else {
            this.isProcessing = false;
        }
        
        this.metrics.batchedUpdates++;
    }

    /**
     * Update frame performance metrics
     * @param {number} frameTime - Frame processing time in ms
     */
    updateFrameMetrics(frameTime) {
        this.metrics.averageFrameTime = 
            (this.metrics.averageFrameTime * (this.metrics.batchedUpdates - 1) + frameTime) / 
            this.metrics.batchedUpdates;
    }

    /**
     * Process search results asynchronously
     * @param {Array} conversations - Array of conversations
     * @param {Function} callback - Callback when processing is complete
     */
    processSearchResultsAsync(conversations, callback) {
        if (this.worker) {
            // Use Web Worker for heavy processing
            this.worker.postMessage({
                type: 'processSearchResults',
                data: conversations
            });
            
            // Store callback for when worker completes
            this.pendingCallbacks = this.pendingCallbacks || new Map();
            this.pendingCallbacks.set('searchResultsProcessed', callback);
            
        } else {
            // Fallback to main thread with batching
            this.queueUpdate(() => {
                const processed = this.processSearchResultsMainThread(conversations);
                callback(processed);
            }, 8, { conversations });
        }
    }

    /**
     * Process search results on main thread (fallback)
     * @param {Array} conversations - Array of conversations
     * @returns {Array} Processed conversations
     */
    processSearchResultsMainThread(conversations) {
        return conversations.map(conv => ({
            ...conv,
            processedTitle: conv.title ? conv.title.substring(0, 100) : 'Untitled',
            processedPrompt: conv.prompt ? conv.prompt.substring(0, 200) : '',
            processedResponse: conv.response ? conv.response.substring(0, 300) : ''
        }));
    }

    /**
     * Apply processed search results to DOM
     * @param {Array} processedResults - Processed search results
     */
    applyProcessedSearchResults(processedResults) {
        const callback = this.pendingCallbacks?.get('searchResultsProcessed');
        if (callback) {
            callback(processedResults);
            this.pendingCallbacks.delete('searchResultsProcessed');
        }
    }

    /**
     * Apply generated HTML to DOM
     * @param {Array} htmlData - Generated HTML data
     */
    applyGeneratedHTML(htmlData) {
        const callback = this.pendingCallbacks?.get('htmlGenerated');
        if (callback) {
            callback(htmlData);
            this.pendingCallbacks.delete('htmlGenerated');
        }
    }

    /**
     * Batch multiple DOM updates together
     * @param {Array} updates - Array of update functions
     * @param {number} priority - Batch priority
     */
    batchUpdates(updates, priority = 5) {
        const batchFunction = (context) => {
            updates.forEach((updateFn, index) => {
                try {
                    updateFn(context);
                } catch (error) {
                    this.logger.error(`Batch update ${index} failed:`, error);
                }
            });
        };
        
        this.queueUpdate(batchFunction, priority, { batchSize: updates.length });
    }

    /**
     * Clear all pending updates
     */
    clearQueue() {
        this.updateQueue = [];
        if (this.frameId) {
            cancelAnimationFrame(this.frameId);
            this.frameId = null;
        }
        this.isProcessing = false;
        this.logger.log('DOM update queue cleared');
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            queueLength: this.updateQueue.length,
            isProcessing: this.isProcessing,
            workerAvailable: !!this.worker
        };
    }

    /**
     * Destroy the updater and clean up resources
     */
    destroy() {
        this.clearQueue();
        
        if (this.worker) {
            this.worker.terminate();
            this.worker = null;
        }
        
        this.pendingCallbacks?.clear();
        this.logger.log('AsyncDOMUpdater destroyed');
    }
}

// Export singleton instance
export const asyncDOMUpdater = new AsyncDOMUpdater();
export { AsyncDOMUpdater };
