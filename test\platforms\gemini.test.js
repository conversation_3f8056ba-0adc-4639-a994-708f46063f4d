/**
 * Unit Tests for Gemini Platform Module
 *
 * Tests the Gemini-specific conversation parsing functionality.
 */

import { describe, test, expect, jest, beforeEach } from '@jest/globals';

// Mock logger
const mockLogger = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
};

describe('Gemini Platform Module', () => {
  let geminiModule;

  beforeEach(async () => {
    jest.clearAllMocks();

    try {
      const module = await import('../../scripts/capture/platforms/gemini.js');
      geminiModule = module;
    } catch (error) {
      // Mock the module structure if import fails
      geminiModule = {
        config: {
          name: 'Gemini',
          apiEndpoint: '/_/BardChatUi/data/assistant.lamda.BardFrontendService/StreamGenerate',
        },
        parseRequest: jest.fn(),
        parseResponse: jest.fn(),
      };
    }
  });

  describe('Module configuration', () => {
    test('should have correct configuration', () => {
      expect(geminiModule.config).toBeDefined();
      expect(geminiModule.config.name).toBe('Gemini');
      expect(geminiModule.config.apiEndpoint).toBe('/_/BardChatUi/data/assistant.lamda.BardFrontendService/StreamGenerate');
    });
  });

  describe('parseRequest', () => {
    test('should extract prompt from Gemini form data', async () => {
      const mockFormData = new Map();
      const promptData = [['What is machine learning?']];
      const fReqData = ['', JSON.stringify(promptData)];
      mockFormData.set('f.req', JSON.stringify(fReqData));

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      if (typeof geminiModule.parseRequest === 'function') {
        const result = await geminiModule.parseRequest(mockRequest, mockLogger);
        expect(result).toBe('What is machine learning?');
        expect(mockLogger.log).toHaveBeenCalledWith(
          'Extracted Gemini prompt:',
          { prompt: 'What is machine learning?', length: 'What is machine learning?'.length }
        );
      }
    });

    test('should handle complex nested prompt structure', async () => {
      const mockFormData = new Map();
      const complexPrompt = 'Explain quantum computing in detail with examples';
      const promptData = [[complexPrompt, 'additional', 'data']];
      const fReqData = ['metadata', JSON.stringify(promptData), 'other'];
      mockFormData.set('f.req', JSON.stringify(fReqData));

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      if (typeof geminiModule.parseRequest === 'function') {
        const result = await geminiModule.parseRequest(mockRequest, mockLogger);
        expect(result).toBe(complexPrompt);
      }
    });

    test('should handle missing f.req parameter', async () => {
      const mockFormData = new Map();
      // No f.req parameter

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      if (typeof geminiModule.parseRequest === 'function') {
        const result = await geminiModule.parseRequest(mockRequest, mockLogger);
        expect(result).toBe('');
        expect(mockLogger.log).toHaveBeenCalledWith('Could not extract Gemini prompt.');
      }
    });

    test('should handle malformed f.req JSON', async () => {
      const mockFormData = new Map();
      mockFormData.set('f.req', 'invalid json');

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      if (typeof geminiModule.parseRequest === 'function') {
        const result = await geminiModule.parseRequest(mockRequest, mockLogger);
        expect(result).toBe('');
        expect(mockLogger.error).toHaveBeenCalled();
      }
    });

    test('should handle malformed nested JSON structure', async () => {
      const mockFormData = new Map();
      const fReqData = ['', 'invalid nested json'];
      mockFormData.set('f.req', JSON.stringify(fReqData));

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      if (typeof geminiModule.parseRequest === 'function') {
        const result = await geminiModule.parseRequest(mockRequest, mockLogger);
        expect(result).toBe('');
        expect(mockLogger.error).toHaveBeenCalled();
      }
    });

    test('should handle unexpected data structure', async () => {
      const mockFormData = new Map();
      const fReqData = ['only one element']; // Missing second element
      mockFormData.set('f.req', JSON.stringify(fReqData));

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      if (typeof geminiModule.parseRequest === 'function') {
        const result = await geminiModule.parseRequest(mockRequest, mockLogger);
        expect(result).toBe('');
      }
    });
  });

  describe('parseResponse', () => {
    test('should parse Gemini streaming response', async () => {
      const mockResponseText = `)]}'

[["wrb.fr","BardFrontendService","StreamGenerate",null,null,null,"generic"],["di",123],["af.httprm",123,"456",null,null,null,null,null,null,123]]

[null,null,null,null,null,[["rc","conversation-id-123"],["r","This is the AI response from Gemini."],["t","Response Title"]]]

[["di",124]]`;

      const mockResponse = {
        clone: () => ({
          text: () => Promise.resolve(mockResponseText)
        })
      };

      if (typeof geminiModule.parseResponse === 'function') {
        const result = await geminiModule.parseResponse(mockResponse, mockLogger);
        expect(result.text).toBe('This is the AI response from Gemini.');
        expect(result.id).toBe('conversation-id-123');
      }
    });

    test('should handle response with multiple data chunks', async () => {
      const mockResponseText = `)]}'

[["wrb.fr","BardFrontendService","StreamGenerate"]]

[null,null,null,null,null,[["rc","multi-chunk-id"],["r","First part of response. "]]]

[null,null,null,null,null,[["r","Second part of response."]]]`;

      const mockResponse = {
        clone: () => ({
          text: () => Promise.resolve(mockResponseText)
        })
      };

      if (typeof geminiModule.parseResponse === 'function') {
        const result = await geminiModule.parseResponse(mockResponse, mockLogger);
        expect(result.text).toBe('First part of response. Second part of response.');
        expect(result.id).toBe('multi-chunk-id');
      }
    });

    test('should handle empty response', async () => {
      const mockResponseText = '';

      const mockResponse = {
        clone: () => ({
          text: () => Promise.resolve(mockResponseText)
        })
      };

      if (typeof geminiModule.parseResponse === 'function') {
        const result = await geminiModule.parseResponse(mockResponse, mockLogger);
        expect(result.text).toBe('');
        expect(result.id).toBe(null); // Gemini returns null for empty responses
      }
    });

    test('should handle response with no valid JSON', async () => {
      const mockResponseText = `)]}'

invalid json line
another invalid line`;

      const mockResponse = {
        clone: () => ({
          text: () => Promise.resolve(mockResponseText)
        })
      };

      if (typeof geminiModule.parseResponse === 'function') {
        const result = await geminiModule.parseResponse(mockResponse, mockLogger);
        expect(result.text).toBe('');
        expect(result.id).toBe(null);
      }
    });

    test('should handle response with malformed JSON arrays', async () => {
      const mockResponseText = `)]}'

[invalid json array
[["valid", "but", "irrelevant"]]`;

      const mockResponse = {
        clone: () => ({
          text: () => Promise.resolve(mockResponseText)
        })
      };

      if (typeof geminiModule.parseResponse === 'function') {
        const result = await geminiModule.parseResponse(mockResponse, mockLogger);
        expect(result.text).toBe('');
        expect(result.id).toBe(null);
      }
    });

    test('should handle response parsing errors', async () => {
      const mockResponse = {
        clone: () => ({
          text: () => Promise.reject(new Error('Network error'))
        })
      };

      if (typeof geminiModule.parseResponse === 'function') {
        const result = await geminiModule.parseResponse(mockResponse, mockLogger);
        expect(result.text).toBe('');
        expect(result.id).toBe(null);
        expect(mockLogger.error).toHaveBeenCalled();
      }
    });
  });

  describe('Integration scenarios', () => {
    test('should handle complete request-response cycle', async () => {
      // Mock request
      const mockFormData = new Map();
      const promptData = [['How does photosynthesis work?']];
      const fReqData = ['', JSON.stringify(promptData)];
      mockFormData.set('f.req', JSON.stringify(fReqData));

      const mockRequest = {
        clone: () => ({
          formData: () => Promise.resolve(mockFormData)
        })
      };

      // Mock response
      const mockResponseText = `)]}'

[null,null,null,null,null,[["rc","photosynthesis-123"],["r","Photosynthesis is the process by which plants convert sunlight into energy."]]]`;

      const mockResponse = {
        clone: () => ({
          text: () => Promise.resolve(mockResponseText)
        })
      };

      if (typeof geminiModule.parseRequest === 'function' &&
          typeof geminiModule.parseResponse === 'function') {
        const prompt = await geminiModule.parseRequest(mockRequest, mockLogger);
        const response = await geminiModule.parseResponse(mockResponse, mockLogger);

        expect(prompt).toBe('How does photosynthesis work?');
        expect(response.text).toBe('Photosynthesis is the process by which plants convert sunlight into energy.');
        expect(response.id).toBe('photosynthesis-123');
      }
    });
  });
});