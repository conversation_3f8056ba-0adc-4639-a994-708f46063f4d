/**
 * Unit Tests for Log Storage Module
 *
 * Tests the session-based logging functionality with Chrome storage integration.
 */

import { describe, test, expect, jest, beforeEach } from '@jest/globals';
import { addLog, getLogs, clearLogs } from '../../modules/log-storage.js';

describe('Log Storage Module', () => {
  let mockSessionStorage;

  beforeEach(() => {
    // Reset Chrome storage mocks
    global.testHelpers.resetChromeMocks();
    
    // Create a mock session storage that behaves like Chrome's session storage
    mockSessionStorage = {};
    
    chrome.storage.session.get.mockImplementation((keys) => {
      if (typeof keys === 'string') {
        return Promise.resolve({ [keys]: mockSessionStorage[keys] });
      } else if (Array.isArray(keys)) {
        const result = {};
        keys.forEach(key => {
          if (key in mockSessionStorage) {
            result[key] = mockSessionStorage[key];
          }
        });
        return Promise.resolve(result);
      }
      return Promise.resolve(mockSessionStorage);
    });
    
    chrome.storage.session.set.mockImplementation((data) => {
      Object.assign(mockSessionStorage, data);
      return Promise.resolve();
    });
    
    chrome.storage.session.remove.mockImplementation((keys) => {
      if (typeof keys === 'string') {
        delete mockSessionStorage[keys];
      } else if (Array.isArray(keys)) {
        keys.forEach(key => delete mockSessionStorage[key]);
      }
      return Promise.resolve();
    });
  });

  describe('addLog', () => {
    test('should add log entry to empty storage', async () => {
      const logEntry = {
        timestamp: new Date().toISOString(),
        level: 'info',
        message: 'Test log message',
      };
      
      const result = await addLog(logEntry);
      
      expect(result).toEqual({ status: 'success' });
      expect(chrome.storage.session.set).toHaveBeenCalledWith({
        'llmlog_debug_logs': [logEntry]
      });
    });

    test('should append log entry to existing logs', async () => {
      const existingLogs = [
        { timestamp: '2025-01-01T00:00:00.000Z', level: 'info', message: 'Existing log' }
      ];
      mockSessionStorage['llmlog_debug_logs'] = existingLogs;
      
      const newLogEntry = {
        timestamp: new Date().toISOString(),
        level: 'error',
        message: 'New log message',
      };
      
      const result = await addLog(newLogEntry);
      
      expect(result).toEqual({ status: 'success' });
      expect(chrome.storage.session.set).toHaveBeenCalledWith({
        'llmlog_debug_logs': [...existingLogs, newLogEntry]
      });
    });

    test('should trim logs when exceeding maximum count', async () => {
      // Create 500 existing logs (at the limit)
      const existingLogs = Array.from({ length: 500 }, (_, i) => ({
        timestamp: new Date(Date.now() - (500 - i) * 1000).toISOString(),
        level: 'info',
        message: `Log ${i}`,
      }));
      mockSessionStorage['llmlog_debug_logs'] = existingLogs;
      
      const newLogEntry = {
        timestamp: new Date().toISOString(),
        level: 'info',
        message: 'New log that should trigger trimming',
      };
      
      const result = await addLog(newLogEntry);
      
      expect(result).toEqual({ status: 'success' });
      
      // Should have called set with exactly 500 logs (trimmed the oldest)
      const setCall = chrome.storage.session.set.mock.calls[0][0];
      const savedLogs = setCall['llmlog_debug_logs'];
      
      expect(savedLogs).toHaveLength(500);
      expect(savedLogs[savedLogs.length - 1]).toEqual(newLogEntry);
      // The first log should be removed, so the new first log should be the old second log
      expect(savedLogs[0]).toEqual(existingLogs[1]);
    });

    test('should handle different log entry formats', async () => {
      const testCases = [
        { timestamp: '2025-01-01T00:00:00.000Z', level: 'debug', message: 'Debug message' },
        { timestamp: '2025-01-01T00:00:01.000Z', level: 'warn', message: 'Warning message', data: { key: 'value' } },
        { timestamp: '2025-01-01T00:00:02.000Z', level: 'error', message: 'Error message', error: new Error('Test error') },
        { customField: 'custom value', message: 'Custom log format' },
      ];
      
      for (const logEntry of testCases) {
        const result = await addLog(logEntry);
        expect(result).toEqual({ status: 'success' });
      }
    });

    test('should handle Chrome storage errors', async () => {
      const logEntry = { message: 'Test log' };
      
      // Mock storage error
      chrome.storage.session.set.mockRejectedValue(new Error('Storage quota exceeded'));
      
      const result = await addLog(logEntry);
      
      expect(result).toEqual({
        status: 'error',
        error: 'Storage quota exceeded'
      });
    });

    test('should handle malformed existing logs', async () => {
      // Set malformed data in storage
      mockSessionStorage['llmlog_debug_logs'] = 'not an array';

      const logEntry = { message: 'Test log' };
      const result = await addLog(logEntry);

      // The actual implementation will fail because it tries to call .push on a string
      expect(result.status).toBe('error');
      expect(result.error).toContain('push is not a function');
    });
  });

  describe('getLogs', () => {
    test('should return empty array when no logs exist', async () => {
      const result = await getLogs();
      
      expect(result).toEqual({
        status: 'success',
        data: []
      });
    });

    test('should return logs in reverse order (newest first)', async () => {
      const logs = [
        { timestamp: '2025-01-01T00:00:00.000Z', message: 'First log' },
        { timestamp: '2025-01-01T00:00:01.000Z', message: 'Second log' },
        { timestamp: '2025-01-01T00:00:02.000Z', message: 'Third log' },
      ];
      mockSessionStorage['llmlog_debug_logs'] = logs;
      
      const result = await getLogs();
      
      expect(result).toEqual({
        status: 'success',
        data: logs.reverse() // Should be reversed
      });
    });

    test('should handle Chrome storage errors', async () => {
      chrome.storage.session.get.mockRejectedValue(new Error('Storage access denied'));
      
      const result = await getLogs();
      
      expect(result).toEqual({
        status: 'error',
        error: 'Storage access denied'
      });
    });

    test('should handle malformed log data', async () => {
      mockSessionStorage['llmlog_debug_logs'] = 'not an array';

      const result = await getLogs();

      // The actual implementation will fail because it tries to call .reverse() on a string
      expect(result.status).toBe('error');
      expect(result.error).toBeDefined();
    });

    test('should return large number of logs efficiently', async () => {
      const largeLogs = Array.from({ length: 1000 }, (_, i) => ({
        timestamp: new Date(Date.now() - (1000 - i) * 1000).toISOString(),
        message: `Log ${i}`,
      }));
      mockSessionStorage['llmlog_debug_logs'] = largeLogs;
      
      const result = await getLogs();
      
      expect(result.status).toBe('success');
      expect(result.data).toHaveLength(1000);
      expect(result.data[0]).toEqual(largeLogs[largeLogs.length - 1]); // Newest first
    });
  });

  describe('clearLogs', () => {
    test('should clear all logs from storage', async () => {
      // Add some logs first
      mockSessionStorage['llmlog_debug_logs'] = [
        { message: 'Log 1' },
        { message: 'Log 2' },
      ];
      
      const result = await clearLogs();
      
      expect(result).toEqual({ status: 'success' });
      expect(chrome.storage.session.remove).toHaveBeenCalledWith('llmlog_debug_logs');
    });

    test('should succeed even when no logs exist', async () => {
      const result = await clearLogs();
      
      expect(result).toEqual({ status: 'success' });
      expect(chrome.storage.session.remove).toHaveBeenCalledWith('llmlog_debug_logs');
    });

    test('should handle Chrome storage errors', async () => {
      chrome.storage.session.remove.mockRejectedValue(new Error('Storage access denied'));
      
      const result = await clearLogs();
      
      expect(result).toEqual({
        status: 'error',
        error: 'Storage access denied'
      });
    });
  });

  describe('Integration tests', () => {
    test('should add, retrieve, and clear logs in sequence', async () => {
      // Add logs
      const log1 = { timestamp: '2025-01-01T00:00:00.000Z', message: 'First log' };
      const log2 = { timestamp: '2025-01-01T00:00:01.000Z', message: 'Second log' };
      
      await addLog(log1);
      await addLog(log2);
      
      // Retrieve logs
      const getResult = await getLogs();
      expect(getResult.status).toBe('success');
      expect(getResult.data).toHaveLength(2);
      expect(getResult.data[0]).toEqual(log2); // Newest first
      expect(getResult.data[1]).toEqual(log1);
      
      // Clear logs
      const clearResult = await clearLogs();
      expect(clearResult.status).toBe('success');
      
      // Verify logs are cleared
      const getAfterClear = await getLogs();
      expect(getAfterClear.status).toBe('success');
      expect(getAfterClear.data).toHaveLength(0);
    });

    test('should handle concurrent log additions', async () => {
      const logs = Array.from({ length: 10 }, (_, i) => ({
        timestamp: new Date(Date.now() + i * 1000).toISOString(),
        message: `Concurrent log ${i}`,
      }));
      
      // Add logs concurrently
      const promises = logs.map(log => addLog(log));
      const results = await Promise.all(promises);
      
      // All should succeed
      results.forEach(result => {
        expect(result.status).toBe('success');
      });
    });
  });
});
