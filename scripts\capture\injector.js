/**
 * Injector Script (Content Script - Isolated World)
 * 
 * This script's sole responsibility is to inject the interceptor script
 * into the main world of the page, allowing it to interact with the
 * page's native JavaScript environment. It also determines which
 * platform-specific module to load.
 */

const PLATFORM_CONFIG = {
    'chat.openai.com': 'chatgpt.js',
    'chatgpt.com': 'chatgpt.js',
    'gemini.google.com': 'gemini.js',
    'claude.ai': 'claude.js',
    'www.tongyi.com': 'tongyi.js',
    'chat.deepseek.com': 'deepseek.js',
    'www.kimi.com': 'kimi.js'
};

function getPlatformModule() {
    const hostname = window.location.hostname;
    const moduleName = PLATFORM_CONFIG[hostname];
    if (moduleName) {
        return chrome.runtime.getURL(`scripts/capture/platforms/${moduleName}`);
    }
    return null;
}

function initialize() {
    chrome.runtime.sendMessage({
        namespace: 'settings',
        action: 'get',
        payload: { key: 'debugLoggingEnabled' }
    }, (debugLoggingEnabled) => {
        
        function injectInterceptor() {
            try {
                const script = document.createElement('script');
                script.src = chrome.runtime.getURL('scripts/capture/interceptor.js');
                script.type = 'module';
                (document.head || document.documentElement).appendChild(script);
                if (debugLoggingEnabled) {
                    console.log('LLMLog Injector: Interceptor script injected.');
                }
            } catch (e) {
                console.error('LLMLog Injector: Failed to inject interceptor script.', e);
            }
        }

        // 1. Wait for the interceptor to be ready
        window.addEventListener('message', async (event) => {
            if (event.source === window && event.data.type === 'LLMLOG_INTERCEPTOR_READY') {
                if (debugLoggingEnabled) {
                    console.log('LLMLog Injector: Interceptor is ready. Determining platform and debug status...');
                }
                
                const modulePath = getPlatformModule();

                if (modulePath) {
                    const loggerPath = chrome.runtime.getURL('modules/logger.js');
                    if (debugLoggingEnabled) {
                        console.log(`LLMLog Injector: Platform detected. Sending module path and debug status.`, { modulePath, debugLoggingEnabled, loggerPath });
                    }
                    // 2. Send the platform module path and debug status to the interceptor
                    window.postMessage({
                        type: 'LLMLOG_INIT',
                        payload: {
                            modulePath,
                            loggerPath,
                            debugMode: debugLoggingEnabled
                        }
                    }, window.location.origin);
                } else {
                    if (debugLoggingEnabled) {
                        console.log('LLMLog Injector: No platform module found for this host.');
                    }
                }
            }
        });

        // 3. Inject the interceptor script itself
        injectInterceptor();
    });
}

initialize();
