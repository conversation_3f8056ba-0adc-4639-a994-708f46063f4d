/**
 * Search Result Cache Module
 * 
 * Intelligent caching system for search results with LRU eviction,
 * cache invalidation, and performance optimization.
 */

import { createLogger } from './logger.js';

class SearchCache {
    constructor(debugMode = false) {
        this.logger = createLogger(debugMode);
        this.cache = new Map();
        this.accessOrder = new Map(); // Track access order for LRU
        this.maxCacheSize = 200;
        this.maxCacheAge = 5 * 60 * 1000; // 5 minutes
        
        // Cache statistics
        this.stats = {
            hits: 0,
            misses: 0,
            evictions: 0,
            invalidations: 0,
            totalQueries: 0
        };
        
        // Cache invalidation triggers
        this.invalidationTriggers = new Set([
            'conversation_added',
            'conversation_deleted',
            'conversation_updated'
        ]);
        
        // Start periodic cleanup
        this.startPeriodicCleanup();
    }

    /**
     * Get cached search results
     * @param {string} searchKey - Search cache key
     * @returns {Object|null} Cached results or null if not found/expired
     */
    get(searchKey) {
        this.stats.totalQueries++;
        
        const cacheEntry = this.cache.get(searchKey);
        
        if (!cacheEntry) {
            this.stats.misses++;
            this.logger.log('Cache miss', { searchKey });
            return null;
        }
        
        // Check if cache entry has expired
        if (this.isExpired(cacheEntry)) {
            this.cache.delete(searchKey);
            this.accessOrder.delete(searchKey);
            this.stats.misses++;
            this.stats.evictions++;
            this.logger.log('Cache entry expired', { searchKey, age: Date.now() - cacheEntry.timestamp });
            return null;
        }
        
        // Update access order for LRU
        this.updateAccessOrder(searchKey);
        
        this.stats.hits++;
        this.logger.log('Cache hit', { 
            searchKey, 
            resultCount: cacheEntry.results.length,
            age: Date.now() - cacheEntry.timestamp 
        });
        
        return cacheEntry.results;
    }

    /**
     * Store search results in cache
     * @param {string} searchKey - Search cache key
     * @param {Array} results - Search results to cache
     * @param {Object} metadata - Additional metadata
     */
    set(searchKey, results, metadata = {}) {
        // Ensure we don't exceed cache size
        this.enforceMaxSize();
        
        const cacheEntry = {
            results: this.cloneResults(results),
            timestamp: Date.now(),
            metadata: {
                resultCount: results.length,
                searchTerms: metadata.searchTerms || [],
                platform: metadata.platform || null,
                ...metadata
            }
        };
        
        this.cache.set(searchKey, cacheEntry);
        this.updateAccessOrder(searchKey);
        
        this.logger.log('Cache entry stored', { 
            searchKey, 
            resultCount: results.length,
            cacheSize: this.cache.size 
        });
    }

    /**
     * Generate cache key for search parameters
     * @param {Object} searchParams - Search parameters
     * @returns {string} Cache key
     */
    generateKey(searchParams) {
        const { search = '', platform = '', page = 1, limit = 50, sortBy = 'createdAt' } = searchParams;
        
        // Normalize search term
        const normalizedSearch = search.toLowerCase().trim();
        
        // Create deterministic key
        const keyParts = [
            `search:${normalizedSearch}`,
            `platform:${platform}`,
            `page:${page}`,
            `limit:${limit}`,
            `sort:${sortBy}`
        ];
        
        return keyParts.join('|');
    }

    /**
     * Invalidate cache entries based on triggers
     * @param {string} trigger - Invalidation trigger
     * @param {Object} context - Context data for invalidation
     */
    invalidate(trigger, context = {}) {
        if (!this.invalidationTriggers.has(trigger)) {
            return;
        }
        
        let invalidatedCount = 0;
        
        switch (trigger) {
            case 'conversation_added':
            case 'conversation_deleted':
                // Invalidate all search caches as results may have changed
                this.clearAll();
                invalidatedCount = this.cache.size;
                break;
                
            case 'conversation_updated':
                // Invalidate caches that might contain the updated conversation
                invalidatedCount = this.invalidateByConversationId(context.conversationId);
                break;
        }
        
        this.stats.invalidations += invalidatedCount;
        this.logger.log('Cache invalidated', { trigger, invalidatedCount, context });
    }

    /**
     * Invalidate cache entries that might contain a specific conversation
     * @param {number} conversationId - Conversation ID
     * @returns {number} Number of invalidated entries
     */
    invalidateByConversationId(conversationId) {
        let invalidatedCount = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            // Check if any cached result contains this conversation
            const containsConversation = entry.results.some(result => result.id === conversationId);
            
            if (containsConversation) {
                this.cache.delete(key);
                this.accessOrder.delete(key);
                invalidatedCount++;
            }
        }
        
        return invalidatedCount;
    }

    /**
     * Clear all cache entries
     */
    clearAll() {
        const size = this.cache.size;
        this.cache.clear();
        this.accessOrder.clear();
        this.logger.log('Cache cleared', { clearedEntries: size });
    }

    /**
     * Check if cache entry has expired
     * @param {Object} cacheEntry - Cache entry to check
     * @returns {boolean} True if expired
     */
    isExpired(cacheEntry) {
        return (Date.now() - cacheEntry.timestamp) > this.maxCacheAge;
    }

    /**
     * Update access order for LRU eviction
     * @param {string} key - Cache key
     */
    updateAccessOrder(key) {
        this.accessOrder.delete(key);
        this.accessOrder.set(key, Date.now());
    }

    /**
     * Enforce maximum cache size using LRU eviction
     */
    enforceMaxSize() {
        while (this.cache.size >= this.maxCacheSize) {
            // Get least recently used key
            const lruKey = this.accessOrder.keys().next().value;
            
            if (lruKey) {
                this.cache.delete(lruKey);
                this.accessOrder.delete(lruKey);
                this.stats.evictions++;
                this.logger.log('Cache entry evicted (LRU)', { evictedKey: lruKey });
            } else {
                break; // Safety break
            }
        }
    }

    /**
     * Clone search results to prevent external modifications
     * @param {Array} results - Original results
     * @returns {Array} Cloned results
     */
    cloneResults(results) {
        return results.map(result => ({
            ...result,
            // Deep clone only essential fields to save memory
            id: result.id,
            title: result.title,
            platform: result.platform,
            createdAt: result.createdAt,
            url: result.url,
            // Store truncated content for cache efficiency
            prompt: result.prompt ? result.prompt.substring(0, 500) : null,
            response: result.response ? result.response.substring(0, 1000) : null,
            _searchScore: result._searchScore,
            _matchedFields: result._matchedFields ? [...result._matchedFields] : null
        }));
    }

    /**
     * Start periodic cleanup of expired entries
     */
    startPeriodicCleanup() {
        setInterval(() => {
            this.cleanupExpiredEntries();
        }, 60000); // Run every minute
    }

    /**
     * Clean up expired cache entries
     */
    cleanupExpiredEntries() {
        let cleanedCount = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            if (this.isExpired(entry)) {
                this.cache.delete(key);
                this.accessOrder.delete(key);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            this.stats.evictions += cleanedCount;
            this.logger.log('Expired cache entries cleaned', { cleanedCount });
        }
    }

    /**
     * Get cache statistics
     * @returns {Object} Cache statistics
     */
    getStats() {
        const hitRate = this.stats.totalQueries > 0 ? 
            (this.stats.hits / this.stats.totalQueries) * 100 : 0;
        
        return {
            ...this.stats,
            hitRate: parseFloat(hitRate.toFixed(2)),
            cacheSize: this.cache.size,
            maxCacheSize: this.maxCacheSize,
            memoryUsage: this.estimateMemoryUsage()
        };
    }

    /**
     * Estimate cache memory usage
     * @returns {number} Estimated memory usage in bytes
     */
    estimateMemoryUsage() {
        let totalSize = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            // Rough estimation
            totalSize += key.length * 2; // UTF-16 characters
            totalSize += JSON.stringify(entry).length * 2;
        }
        
        return totalSize;
    }

    /**
     * Optimize cache by removing low-value entries
     */
    optimize() {
        const beforeSize = this.cache.size;
        
        // Remove entries with very few results (likely not useful)
        for (const [key, entry] of this.cache.entries()) {
            if (entry.results.length === 0) {
                this.cache.delete(key);
                this.accessOrder.delete(key);
            }
        }
        
        const afterSize = this.cache.size;
        const optimizedCount = beforeSize - afterSize;
        
        if (optimizedCount > 0) {
            this.logger.log('Cache optimized', { removedEntries: optimizedCount });
        }
    }

    /**
     * Preload cache with common search patterns
     * @param {Array} commonSearches - Array of common search terms
     */
    async preloadCommonSearches(commonSearches) {
        this.logger.log('Preloading common searches', { count: commonSearches.length });
        
        // This would be called with actual search function
        // Implementation depends on integration with search system
    }
}

// Export singleton instance
export const searchCache = new SearchCache();
export { SearchCache };
