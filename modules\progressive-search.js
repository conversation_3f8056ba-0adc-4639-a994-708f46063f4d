/**
 * Progressive Search Loading Module
 * 
 * Implements progressive search results loading with streaming updates,
 * early result display, and improved perceived performance.
 */

import { createLogger } from './logger.js';
import { searchCache } from './search-cache.js';
import { asyncDOMUpdater } from './async-dom-updater.js';

class ProgressiveSearch {
    constructor(debugMode = false) {
        this.logger = createLogger(debugMode);
        this.activeSearches = new Map();
        this.searchId = 0;
        
        // Configuration
        this.config = {
            batchSize: 20,           // Results per batch
            batchDelay: 50,          // ms between batches
            maxConcurrentSearches: 3,
            earlyResultThreshold: 5,  // Show results after this many found
            streamingEnabled: true,
            priorityBoost: 2.0       // Boost for high-priority searches
        };
        
        // Performance tracking
        this.metrics = {
            totalSearches: 0,
            averageSearchTime: 0,
            streamingSearches: 0,
            earlyResults: 0,
            cacheHits: 0
        };
        
        // Search state
        this.currentSearch = null;
        this.searchQueue = [];
        this.resultCallbacks = new Map();
    }

    /**
     * Perform progressive search with streaming results
     * @param {Object} searchParams - Search parameters
     * @param {Function} onResults - Callback for receiving results
     * @param {Function} onComplete - Callback when search is complete
     * @param {Object} options - Search options
     * @returns {string} Search ID for cancellation
     */
    async performProgressiveSearch(searchParams, onResults, onComplete, options = {}) {
        const searchId = this.generateSearchId();
        const startTime = performance.now();
        
        try {
            // Check cache first
            const cacheKey = searchCache.generateKey(searchParams);
            const cachedResults = searchCache.get(cacheKey);
            
            if (cachedResults) {
                this.metrics.cacheHits++;
                this.logger.log('Progressive search cache hit', { searchId, cacheKey });
                
                // Stream cached results progressively for consistency
                this.streamCachedResults(cachedResults, onResults, onComplete, searchId);
                return searchId;
            }
            
            // Create search context
            const searchContext = {
                id: searchId,
                params: searchParams,
                options: {
                    priority: options.priority || 5,
                    streaming: options.streaming !== false,
                    ...options
                },
                callbacks: { onResults, onComplete },
                startTime,
                results: [],
                isComplete: false,
                isCancelled: false
            };
            
            this.activeSearches.set(searchId, searchContext);
            
            // Start progressive search
            if (this.config.streamingEnabled && searchContext.options.streaming) {
                await this.performStreamingSearch(searchContext);
            } else {
                await this.performBatchSearch(searchContext);
            }
            
            this.metrics.totalSearches++;
            
            return searchId;
            
        } catch (error) {
            this.logger.error('Progressive search error:', error, { searchId });
            onComplete([], { error: error.message, searchId });
            return searchId;
        }
    }

    /**
     * Perform streaming search with real-time results
     * @param {Object} searchContext - Search context
     */
    async performStreamingSearch(searchContext) {
        const { id, params, callbacks } = searchContext;
        
        this.logger.log('Starting streaming search', { searchId: id, params });
        this.metrics.streamingSearches++;
        
        try {
            // Get database connection and start streaming
            const results = await this.streamSearchFromDatabase(searchContext);
            
            if (!searchContext.isCancelled) {
                // Cache final results
                const cacheKey = searchCache.generateKey(params);
                searchCache.set(cacheKey, results, {
                    searchTerms: params.search ? params.search.split(/\s+/) : [],
                    platform: params.platform
                });
                
                // Final callback
                callbacks.onComplete(results, {
                    searchId: id,
                    totalResults: results.length,
                    searchTime: performance.now() - searchContext.startTime,
                    streaming: true
                });
            }
            
        } catch (error) {
            if (!searchContext.isCancelled) {
                callbacks.onComplete([], { error: error.message, searchId: id });
            }
        } finally {
            this.activeSearches.delete(id);
        }
    }

    /**
     * Stream search results from database
     * @param {Object} searchContext - Search context
     * @returns {Array} Final results array
     */
    async streamSearchFromDatabase(searchContext) {
        const { params, callbacks, options } = searchContext;
        const allResults = [];
        let processedCount = 0;
        let earlyResultsSent = false;
        
        // Simulate database streaming (in real implementation, this would use IndexedDB cursors)
        return new Promise(async (resolve, reject) => {
            try {
                // Get all conversations from storage
                const response = await chrome.runtime.sendMessage({
                    namespace: 'database',
                    action: 'getConversations',
                    payload: { ...params, limit: 1000 } // Get large batch for streaming
                });
                
                if (response.status !== 'success') {
                    reject(new Error(response.message || 'Database error'));
                    return;
                }
                
                const conversations = response.data.conversations || response.data || [];
                
                // Process in batches
                const processBatch = async (startIndex) => {
                    if (searchContext.isCancelled) {
                        resolve(allResults);
                        return;
                    }
                    
                    const endIndex = Math.min(startIndex + this.config.batchSize, conversations.length);
                    const batch = conversations.slice(startIndex, endIndex);
                    
                    // Filter batch based on search criteria
                    const filteredBatch = this.filterBatch(batch, params);
                    
                    if (filteredBatch.length > 0) {
                        allResults.push(...filteredBatch);
                        processedCount += filteredBatch.length;
                        
                        // Send early results if threshold met
                        if (!earlyResultsSent && processedCount >= this.config.earlyResultThreshold) {
                            earlyResultsSent = true;
                            this.metrics.earlyResults++;
                            
                            asyncDOMUpdater.queueUpdate(() => {
                                callbacks.onResults([...allResults], {
                                    isPartial: true,
                                    processedCount,
                                    totalEstimate: conversations.length
                                });
                            }, options.priority + this.config.priorityBoost);
                        }
                        
                        // Send progressive updates
                        if (earlyResultsSent && allResults.length % (this.config.batchSize * 2) === 0) {
                            asyncDOMUpdater.queueUpdate(() => {
                                callbacks.onResults([...allResults], {
                                    isPartial: true,
                                    processedCount,
                                    totalEstimate: conversations.length
                                });
                            }, options.priority);
                        }
                    }
                    
                    // Continue with next batch
                    if (endIndex < conversations.length) {
                        setTimeout(() => processBatch(endIndex), this.config.batchDelay);
                    } else {
                        // Search complete
                        resolve(allResults);
                    }
                };
                
                // Start processing
                processBatch(0);
                
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Filter batch of conversations based on search criteria
     * @param {Array} batch - Batch of conversations
     * @param {Object} params - Search parameters
     * @returns {Array} Filtered conversations
     */
    filterBatch(batch, params) {
        const { search, platform } = params;
        
        if (!search && !platform) {
            return batch;
        }
        
        const searchTerms = search ? search.toLowerCase().split(/\s+/).filter(t => t.length > 0) : [];
        
        return batch.filter(conversation => {
            // Platform filter
            if (platform && conversation.platform !== platform) {
                return false;
            }
            
            // Search filter
            if (searchTerms.length > 0) {
                const matchesSearch = searchTerms.every(term => {
                    return (conversation.title && conversation.title.toLowerCase().includes(term)) ||
                           (conversation.prompt && conversation.prompt.toLowerCase().includes(term)) ||
                           (conversation.response && conversation.response.toLowerCase().includes(term)) ||
                           (conversation.platform && conversation.platform.toLowerCase().includes(term));
                });
                
                if (!matchesSearch) {
                    return false;
                }
            }
            
            return true;
        });
    }

    /**
     * Stream cached results progressively
     * @param {Array} cachedResults - Cached search results
     * @param {Function} onResults - Results callback
     * @param {Function} onComplete - Completion callback
     * @param {string} searchId - Search ID
     */
    streamCachedResults(cachedResults, onResults, onComplete, searchId) {
        let currentIndex = 0;
        
        const streamBatch = () => {
            if (currentIndex >= cachedResults.length) {
                onComplete(cachedResults, {
                    searchId,
                    totalResults: cachedResults.length,
                    fromCache: true
                });
                return;
            }
            
            const endIndex = Math.min(currentIndex + this.config.batchSize, cachedResults.length);
            const batch = cachedResults.slice(0, endIndex);
            
            asyncDOMUpdater.queueUpdate(() => {
                onResults(batch, {
                    isPartial: endIndex < cachedResults.length,
                    processedCount: endIndex,
                    totalEstimate: cachedResults.length,
                    fromCache: true
                });
            }, 8);
            
            currentIndex = endIndex;
            
            if (currentIndex < cachedResults.length) {
                setTimeout(streamBatch, this.config.batchDelay / 2); // Faster for cached results
            } else {
                onComplete(cachedResults, {
                    searchId,
                    totalResults: cachedResults.length,
                    fromCache: true
                });
            }
        };
        
        streamBatch();
    }

    /**
     * Perform batch search (fallback for non-streaming)
     * @param {Object} searchContext - Search context
     */
    async performBatchSearch(searchContext) {
        const { id, params, callbacks } = searchContext;
        
        try {
            const response = await chrome.runtime.sendMessage({
                namespace: 'database',
                action: 'getConversations',
                payload: params
            });
            
            if (response.status === 'success') {
                const results = response.data.conversations || response.data || [];
                
                if (!searchContext.isCancelled) {
                    // Cache results
                    const cacheKey = searchCache.generateKey(params);
                    searchCache.set(cacheKey, results);
                    
                    callbacks.onComplete(results, {
                        searchId: id,
                        totalResults: results.length,
                        searchTime: performance.now() - searchContext.startTime
                    });
                }
            } else {
                throw new Error(response.message || 'Search failed');
            }
            
        } catch (error) {
            if (!searchContext.isCancelled) {
                callbacks.onComplete([], { error: error.message, searchId: id });
            }
        } finally {
            this.activeSearches.delete(id);
        }
    }

    /**
     * Cancel active search
     * @param {string} searchId - Search ID to cancel
     */
    cancelSearch(searchId) {
        const searchContext = this.activeSearches.get(searchId);
        if (searchContext) {
            searchContext.isCancelled = true;
            this.activeSearches.delete(searchId);
            this.logger.log('Search cancelled', { searchId });
        }
    }

    /**
     * Cancel all active searches
     */
    cancelAllSearches() {
        for (const [searchId, context] of this.activeSearches) {
            context.isCancelled = true;
        }
        this.activeSearches.clear();
        this.logger.log('All searches cancelled');
    }

    /**
     * Generate unique search ID
     * @returns {string} Search ID
     */
    generateSearchId() {
        return `search_${++this.searchId}_${Date.now()}`;
    }

    /**
     * Get performance metrics
     * @returns {Object} Performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            activeSearches: this.activeSearches.size,
            cacheHitRate: this.metrics.totalSearches > 0 ? 
                (this.metrics.cacheHits / this.metrics.totalSearches) * 100 : 0
        };
    }

    /**
     * Update configuration
     * @param {Object} newConfig - New configuration options
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.logger.log('Configuration updated', this.config);
    }
}

    /**
     * Destroy progressive search and clean up
     */
    destroy() {
        this.cancelAllSearches();
        this.resultCallbacks.clear();
        this.searchQueue = [];
        this.logger.log('ProgressiveSearch destroyed');
    }
}

// Export singleton instance
export const progressiveSearch = new ProgressiveSearch();
export { ProgressiveSearch };
