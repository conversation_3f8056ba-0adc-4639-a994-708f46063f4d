{"name": "llmlog", "version": "1.0.0", "description": "", "main": "debug.js", "directories": {"lib": "lib"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "fake-indexeddb": "^6.0.0", "@jest/globals": "^29.7.0", "@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-jest": "^29.7.0"}, "scripts": {"build": "tailwindcss -i ./styles/main.css -o ./dist/output.css", "watch": "tailwindcss -i ./styles/main.css -o ./dist/output.css --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose"}, "repository": {"type": "git", "url": "git+https://github.com/dandawong/llmlog.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/dandawong/llmlog/issues"}, "homepage": "https://github.com/dandawong/llmlog#readme", "dependencies": {"@tailwindcss/cli": "^4.1.12"}}