/**
 * Unit Tests for Settings Module
 * 
 * Tests the configuration management functionality with Chrome storage integration.
 */

import { describe, test, expect, jest, beforeEach } from '@jest/globals';
import { getSetting, setSetting } from '../../modules/settings.js';

describe('Settings Module', () => {
  beforeEach(() => {
    // Reset Chrome storage mocks
    global.testHelpers.resetChromeMocks();
  });

  describe('getSetting', () => {
    test('should retrieve existing setting from Chrome storage', async () => {
      const testKey = 'debugLoggingEnabled';
      const testValue = true;
      
      // Mock Chrome storage response
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        const result = { [testKey]: testValue };
        if (callback) callback(result);
        return Promise.resolve(result);
      });
      
      const result = await getSetting(testKey);
      
      expect(result).toBe(testValue);
      expect(chrome.storage.local.get).toHaveBeenCalledWith([testKey], expect.any(Function));
    });

    test('should return default value when setting does not exist', async () => {
      const testKey = 'debugLoggingEnabled';
      const defaultValue = false;
      
      // Mock Chrome storage response with empty result
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        const result = {}; // Empty result simulates missing key
        if (callback) callback(result);
        return Promise.resolve(result);
      });
      
      const result = await getSetting(testKey);
      
      expect(result).toBe(defaultValue);
      expect(chrome.storage.local.get).toHaveBeenCalledWith([testKey], expect.any(Function));
    });

    test('should return undefined for unknown setting key', async () => {
      const unknownKey = 'unknownSetting';
      
      // Mock Chrome storage response with empty result
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        const result = {};
        if (callback) callback(result);
        return Promise.resolve(result);
      });
      
      const result = await getSetting(unknownKey);
      
      expect(result).toBeUndefined();
      expect(chrome.storage.local.get).toHaveBeenCalledWith([unknownKey], expect.any(Function));
    });

    test('should handle Chrome storage errors by using callback pattern', async () => {
      const testKey = 'debugLoggingEnabled';

      // Mock Chrome storage to simulate error by not calling callback
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        // Simulate Chrome runtime error - the actual implementation doesn't handle this
        // but we can test that it doesn't crash
        chrome.runtime.lastError = { message: 'Storage error' };
        if (callback) callback({}); // Still call callback with empty result
      });

      // The actual implementation will return the default value
      const result = await getSetting(testKey);

      expect(result).toBe(false); // Should return default value
    });

    test('should handle different data types', async () => {
      const testCases = [
        { key: 'stringValue', value: 'test string' },
        { key: 'numberValue', value: 42 },
        { key: 'booleanValue', value: true },
        { key: 'objectValue', value: { nested: 'object' } },
        { key: 'arrayValue', value: [1, 2, 3] },
        // Note: null values become undefined due to ?? operator in settings module
      ];
      
      for (const testCase of testCases) {
        chrome.storage.local.get.mockImplementation((keys, callback) => {
          const result = { [testCase.key]: testCase.value };
          if (callback) callback(result);
          return Promise.resolve(result);
        });
        
        const result = await getSetting(testCase.key);
        expect(result).toEqual(testCase.value);
      }
    });
  });

  describe('setSetting', () => {
    test('should save setting to Chrome storage', async () => {
      const testKey = 'debugLoggingEnabled';
      const testValue = true;
      
      // Mock Chrome storage set
      chrome.storage.local.set.mockImplementation((data, callback) => {
        if (callback) callback();
        return Promise.resolve();
      });
      
      await setSetting(testKey, testValue);
      
      expect(chrome.storage.local.set).toHaveBeenCalledWith(
        { [testKey]: testValue },
        expect.any(Function)
      );
    });

    test('should handle different data types when saving', async () => {
      const testCases = [
        { key: 'stringValue', value: 'test string' },
        { key: 'numberValue', value: 42 },
        { key: 'booleanValue', value: false },
        { key: 'objectValue', value: { nested: { deep: 'object' } } },
        { key: 'arrayValue', value: ['a', 'b', 'c'] },
        { key: 'nullValue', value: null },
        { key: 'undefinedValue', value: undefined },
      ];
      
      chrome.storage.local.set.mockImplementation((data, callback) => {
        if (callback) callback();
        return Promise.resolve();
      });
      
      for (const testCase of testCases) {
        await setSetting(testCase.key, testCase.value);
        
        expect(chrome.storage.local.set).toHaveBeenCalledWith(
          { [testCase.key]: testCase.value },
          expect.any(Function)
        );
      }
    });

    test('should handle Chrome storage errors when saving', async () => {
      const testKey = 'debugLoggingEnabled';
      const testValue = true;

      // Mock Chrome storage to simulate error by not calling callback
      chrome.storage.local.set.mockImplementation((data, callback) => {
        chrome.runtime.lastError = { message: 'Storage quota exceeded' };
        if (callback) callback(); // Still call callback to prevent hanging
      });

      // The actual implementation doesn't handle errors, so it should complete normally
      await expect(setSetting(testKey, testValue)).resolves.toBeUndefined();
    });

    test('should overwrite existing settings', async () => {
      const testKey = 'debugLoggingEnabled';
      const initialValue = false;
      const newValue = true;
      
      chrome.storage.local.set.mockImplementation((data, callback) => {
        if (callback) callback();
        return Promise.resolve();
      });
      
      // Set initial value
      await setSetting(testKey, initialValue);
      expect(chrome.storage.local.set).toHaveBeenCalledWith(
        { [testKey]: initialValue },
        expect.any(Function)
      );
      
      // Overwrite with new value
      await setSetting(testKey, newValue);
      expect(chrome.storage.local.set).toHaveBeenCalledWith(
        { [testKey]: newValue },
        expect.any(Function)
      );
    });
  });

  describe('Integration tests', () => {
    test('should save and retrieve setting correctly', async () => {
      const testKey = 'debugLoggingEnabled';
      const testValue = true;
      let storedData = {};
      
      // Mock storage to actually store and retrieve data
      chrome.storage.local.set.mockImplementation((data, callback) => {
        Object.assign(storedData, data);
        if (callback) callback();
        return Promise.resolve();
      });
      
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        const result = {};
        keys.forEach(key => {
          if (key in storedData) {
            result[key] = storedData[key];
          }
        });
        if (callback) callback(result);
        return Promise.resolve(result);
      });
      
      // Save setting
      await setSetting(testKey, testValue);
      
      // Retrieve setting
      const retrievedValue = await getSetting(testKey);
      
      expect(retrievedValue).toBe(testValue);
    });

    test('should handle multiple settings independently', async () => {
      const settings = {
        debugLoggingEnabled: true,
        autoCapture: false,
        maxConversations: 1000,
      };
      let storedData = {};
      
      // Mock storage
      chrome.storage.local.set.mockImplementation((data, callback) => {
        Object.assign(storedData, data);
        if (callback) callback();
        return Promise.resolve();
      });
      
      chrome.storage.local.get.mockImplementation((keys, callback) => {
        const result = {};
        keys.forEach(key => {
          if (key in storedData) {
            result[key] = storedData[key];
          }
        });
        if (callback) callback(result);
        return Promise.resolve(result);
      });
      
      // Save multiple settings
      for (const [key, value] of Object.entries(settings)) {
        await setSetting(key, value);
      }
      
      // Retrieve and verify each setting
      for (const [key, expectedValue] of Object.entries(settings)) {
        const retrievedValue = await getSetting(key);
        expect(retrievedValue).toBe(expectedValue);
      }
    });
  });
});
