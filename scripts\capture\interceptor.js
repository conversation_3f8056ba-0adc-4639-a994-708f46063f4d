let platformModule;
let logger;

// Track recent conversations to prevent duplicates with memory limits
const recentConversations = new Map();
const processedConversationIds = new Set();
const DUPLICATE_WINDOW_MS = 15000; // 15 seconds (balanced for page reloads vs new conversations)
const MAX_TRACKED_CONVERSATIONS = 1000; // Limit memory usage
const MAX_PROCESSED_IDS = 500; // Limit Set size

// Function to check if a conversation is a duplicate
function isDuplicateConversation(conversationData) {
    const now = Date.now();

    // Create multiple keys for different duplicate detection strategies
    const contentKey = `${conversationData.platform}:${conversationData.prompt}:${conversationData.response}`;
    const urlKey = conversationData.url;

    // Extract conversation ID from URL for more robust detection
    let conversationId = null;
    if (conversationData.url) {
        // For ChatGPT: extract from /c/conversation-id
        const chatgptMatch = conversationData.url.match(/\/c\/([a-f0-9-]+)/);
        // For <PERSON>: extract from current URL or conversation data
        const claudeMatch = conversationData.url.match(/\/chat\/([a-f0-9-]+)/);
        // For Gemini: extract from /app/conversation-id
        const geminiMatch = conversationData.url.match(/\/app\/([a-f0-9-]+)/);

        conversationId = chatgptMatch?.[1] || claudeMatch?.[1] || geminiMatch?.[1];
    }

    // Clean up old entries and enforce size limits
    for (const [existingKey, timestamp] of recentConversations.entries()) {
        if (now - timestamp > DUPLICATE_WINDOW_MS) {
            recentConversations.delete(existingKey);
        }
    }

    // Enforce memory limits by removing oldest entries if needed
    if (recentConversations.size > MAX_TRACKED_CONVERSATIONS) {
        const entries = Array.from(recentConversations.entries());
        entries.sort((a, b) => a[1] - b[1]); // Sort by timestamp
        const toRemove = entries.slice(0, entries.length - MAX_TRACKED_CONVERSATIONS);
        toRemove.forEach(([key]) => recentConversations.delete(key));
        logger.log(`Cleaned up ${toRemove.length} old conversation entries for memory optimization`);
    }

    // Clean up processed IDs set if it gets too large
    if (processedConversationIds.size > MAX_PROCESSED_IDS) {
        processedConversationIds.clear();
        logger.log('Cleared processed conversation IDs for memory optimization');
    }

    // Strategy 1: Check by conversation ID + content (most reliable for true duplicates)
    if (conversationId) {
        const idContentKey = `${conversationData.platform}:${conversationId}:${conversationData.prompt}:${conversationData.response}`;
        if (processedConversationIds.has(idContentKey)) {
            logger.log('Duplicate conversation detected by ID + content, skipping.', {
                conversationId,
                platform: conversationData.platform,
                url: conversationData.url,
                reason: 'Same conversation ID with identical content (likely page reload)'
            });
            return true;
        }
        // Mark this specific conversation content as processed
        processedConversationIds.add(idContentKey);

        // Clean up old entries for this conversation ID to prevent memory bloat
        const oldEntries = Array.from(processedConversationIds).filter(key =>
            key.startsWith(`${conversationData.platform}:${conversationId}:`) &&
            key !== idContentKey
        );
        // Keep only the last 10 entries per conversation to allow for conversation continuation
        if (oldEntries.length > 10) {
            oldEntries.slice(0, oldEntries.length - 10).forEach(key => {
                processedConversationIds.delete(key);
            });
        }
    }

    // Strategy 2: Check by content within time window
    if (recentConversations.has(contentKey)) {
        logger.log('Duplicate conversation detected by content, skipping.', {
            contentKey: contentKey.substring(0, 100) + '...'
        });
        return true;
    }

    // Strategy 3: Check by URL within time window (ONLY for page reloads, not new conversations)
    // This should only block if it's the EXACT same content on the same URL within a short time
    if (urlKey && recentConversations.has(contentKey)) {
        // Only block if we have both same URL AND same content within time window
        const urlTimeKey = `url:${urlKey}`;
        if (recentConversations.has(urlTimeKey)) {
            logger.log('Duplicate conversation detected by URL + content, skipping.', {
                url: urlKey,
                reason: 'Same URL and same content within time window (likely page reload)'
            });
            return true;
        }
    }

    // Always track URL with timestamp for reference, but don't block based on URL alone
    if (urlKey) {
        const urlTimeKey = `url:${urlKey}`;
        recentConversations.set(urlTimeKey, now);
    }

    // Add this conversation to the tracking map
    recentConversations.set(contentKey, now);

    logger.log('New conversation detected, processing.', {
        platform: conversationData.platform,
        conversationId,
        hasUrl: !!conversationData.url,
        promptLength: conversationData.prompt.length,
        responseLength: conversationData.response.length
    });

    return false;
}

// 1. Listen for messages from the injector and the platform module
window.addEventListener('message', (event) => {
    if (event.source !== window) return;

    if (event.data.type === 'LLMLOG_INIT') {
        const { modulePath, loggerPath, debugMode } = event.data.payload;
        
        import(loggerPath)
            .then(({ createLogger }) => {
                logger = createLogger(debugMode);
                logger.log('Interceptor initialized.', { debugMode });

                import(modulePath)
                    .then(module => {
                        platformModule = module;
                        logger.log('Platform module loaded.', platformModule.config.name);
                        if (platformModule.config.name === 'Gemini' || platformModule.config.name === 'DeepSeek') {
                            overrideXHR();
                        } else {
                            overrideFetch();
                        }
                    })
                    .catch(e => logger.error('Failed to load platform module.', e));
            })
            .catch(e => console.error('LLMLog: Failed to load logger module.', e));
    }

    if (event.data.type === 'LLMLOG_CONVERSATION_UPDATE') {
        logger.log('Received conversation update from platform module.', event.data.payload);

        // Apply duplicate detection to Claude conversations too!
        const conversationData = event.data.payload;
        if (!isDuplicateConversation(conversationData)) {
            window.postMessage({ type: 'LLMLOG_CONVERSATION', payload: conversationData }, window.location.origin);
            logger.log('Sent Claude conversation data to bridge.', conversationData);
        } else {
            logger.log('Claude conversation blocked as duplicate.', {
                platform: conversationData.platform,
                promptPreview: conversationData.prompt.substring(0, 50) + '...',
                responsePreview: conversationData.response.substring(0, 50) + '...'
            });
        }
    }
});

// 2. Announce that the interceptor is ready and request the config
window.postMessage({ type: 'LLMLOG_INTERCEPTOR_READY' }, window.location.origin);


function overrideFetch() {
    if (!platformModule) {
        logger.error('No platform module loaded.');
        return;
    }

    const { config, parseRequest, parseResponse } = platformModule;
    const originalFetch = window.fetch;

    window.fetch = async (...args) => {
        const url = args[0] instanceof Request ? args[0].url : args[0];
        const method = (args[0] instanceof Request ? args[0].method : (args[1] ? args[1].method : 'GET'))?.toUpperCase();
        const requestUrl = new URL(url, window.location.origin);

        const isMatch = config.apiEndpoint instanceof RegExp
            ? config.apiEndpoint.test(requestUrl.pathname)
            : requestUrl.pathname === config.apiEndpoint;

        // Log requests for debugging
        logger.log('Saw a request.', { 
            method,
            pathname: requestUrl.pathname,
            expected: config.apiEndpoint.toString(),
            match: isMatch
        });

        // Intercept the target API call (POST or GET)
        if (isMatch) {
            const request = new Request(...args);
            logger.log('Target API call detected.', { url: requestUrl.href });

            const userPrompt = await parseRequest(request, logger);
            logger.log('Parsed user prompt.', { prompt: userPrompt });

            const response = await originalFetch(request);
            const responseClone = response.clone();

            const { text: aiResponse, id: conversationId, url: platformUrl } = await parseResponse(responseClone, logger);
            logger.log('Parsed AI response.', { response: aiResponse, conversationId, platformUrl });

            // For Claude, the conversation data is sent via a custom event, so we skip this part.
            if (config.name === 'Claude') {
                return response;
            }

            let conversationUrl = platformUrl || window.location.href;
            if (conversationId) {
                if (config.name === 'Tongyi') {
                    conversationUrl = `${window.location.origin}/?sessionId=${conversationId}`;
                } else if (config.name === 'DeepSeek') {
                    conversationUrl = `${window.location.origin}/a/chat/s/${conversationId}`;
                } else if (config.name === 'Kimi') {
                    // Kimi URL is handled by platformUrl
                } else {
                    conversationUrl = `${window.location.origin}/c/${conversationId}`;
                }
            }

            const conversationData = {
                platform: config.name,
                prompt: userPrompt,
                response: aiResponse,
                url: conversationUrl,
                createdAt: new Date().toISOString(),
                title: userPrompt.substring(0, 50)
            };

            // Check for duplicates before sending
            if (!isDuplicateConversation(conversationData)) {
                window.postMessage({ type: 'LLMLOG_CONVERSATION', payload: conversationData }, window.location.origin);
                logger.log('Sent conversation data to bridge.', conversationData);
            }

            return response; // Return the original response, not a clone
        }

        // For all other requests, pass them through without modification.
        return originalFetch(...args);
    };

    logger.log('`fetch` has been overridden.');
}

function overrideXHR() {
    if (!platformModule) {
        logger.error('No platform module loaded for XHR.');
        return;
    }

    const { config, parseRequest, parseResponse } = platformModule;
    const originalOpen = XMLHttpRequest.prototype.open;
    const originalSend = XMLHttpRequest.prototype.send;

    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._llmlog_method = method;
        this._llmlog_url = url;
        return originalOpen.apply(this, [method, url, ...args]);
    };

    XMLHttpRequest.prototype.send = function(body) {
        const requestUrl = new URL(this._llmlog_url, window.location.origin);
        const method = this._llmlog_method.toUpperCase();

        if (method === 'POST') {
             logger.log('(XHR) Saw a POST request.', { 
                pathname: requestUrl.pathname,
                expected: config.apiEndpoint,
                match: config.apiEndpoint instanceof RegExp
                    ? config.apiEndpoint.test(requestUrl.pathname)
                    : requestUrl.pathname === config.apiEndpoint
            });
        }

        const isMatch = config.apiEndpoint instanceof RegExp
            ? config.apiEndpoint.test(requestUrl.pathname)
            : requestUrl.pathname === config.apiEndpoint;

        if (method === 'POST' && isMatch) {
            logger.log('(XHR) Target API call detected.', { url: requestUrl.href });
            
            // Add the URL to the mock request
            const mockRequest = {
                clone: () => mockRequest,
                json: async () => JSON.parse(body),
                formData: async () => {
                    const params = new URLSearchParams(body);
                    const formData = new FormData();
                    for (const [key, value] of params.entries()) {
                        formData.append(key, value);
                    }
                    return formData;
                },
                url: requestUrl.href
            };

            this.addEventListener('load', async () => {
                if (this.readyState === 4 && this.status === 200) {
                    logger.log('(XHR) Response loaded.');
                    
                    const userPrompt = await parseRequest(mockRequest, logger);
                    logger.log('(XHR) Parsed user prompt.', { prompt: userPrompt });

                    // Mock a Response object for parseResponse
                    const mockResponse = {
                        clone: () => mockResponse,
                        text: async () => this.responseText,
                    };

                    const { text: aiResponse, id: conversationId, url: platformUrl } = await parseResponse(mockResponse, logger);
                    logger.log('(XHR) Parsed AI response.', { response: aiResponse, conversationId, platformUrl });

                    let conversationUrl = platformUrl || window.location.href;
                    if (conversationId) {
                        if (config.name === 'Tongyi') {
                            conversationUrl = `${window.location.origin}/?sessionId=${conversationId}`;
                        } else if (config.name === 'DeepSeek') {
                            conversationUrl = `${window.location.origin}/a/chat/s/${conversationId}`;
                        } else if (config.name === 'Kimi') {
                            // Kimi URL is handled by platformUrl
                        } else {
                            conversationUrl = `${window.location.origin}/c/${conversationId}`;
                        }
                    }

                    const conversationData = {
                        platform: config.name,
                        prompt: userPrompt,
                        response: aiResponse,
                        url: conversationUrl,
                        createdAt: new Date().toISOString(),
                        title: userPrompt.substring(0, 50)
                    };

                    // Check for duplicates before sending
                    if (!isDuplicateConversation(conversationData)) {
                        window.postMessage({ type: 'LLMLOG_CONVERSATION', payload: conversationData }, window.location.origin);
                        logger.log('(XHR) Sent conversation data to bridge.', conversationData);
                    }
                }
            });
        }
        
        return originalSend.apply(this, arguments);
    };
    
    logger.log('`XMLHttpRequest` has been overridden.');
}
